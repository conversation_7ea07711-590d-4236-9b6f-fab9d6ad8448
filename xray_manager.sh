#!/bin/bash

# Xray-core VLESS + Reality + SOCKS5 管理脚本
# 作者: AI Assistant
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置文件路径
XRAY_CONFIG="/usr/local/etc/xray/config.json"
XRAY_SERVICE="/etc/systemd/system/xray.service"
XRAY_BINARY="/usr/local/bin/xray"
SCRIPT_LOG="/var/log/xray_manager.log"

# 全局变量
SELECTED_PORT=""
PROXY_HOST=""
PROXY_PORT=""
PROXY_USER=""
PROXY_PASS=""

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$SCRIPT_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$SCRIPT_LOG"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$SCRIPT_LOG"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$SCRIPT_LOG"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$SCRIPT_LOG"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检测sing-box占用的端口
detect_singbox_ports() {
    info "检测sing-box占用的端口..."
    
    local singbox_ports=()
    
    # 方法1: 通过netstat检测sing-box进程占用的端口
    if command -v netstat &> /dev/null; then
        local netstat_ports
        netstat_ports=$(netstat -tlnp 2>/dev/null | grep sing-box | awk '{print $4}' | sed 's/.*://' | sort -n | uniq)
        if [[ -n "$netstat_ports" ]]; then
            while IFS= read -r port; do
                [[ -n "$port" ]] && singbox_ports+=("$port")
            done <<< "$netstat_ports"
        fi
    fi
    
    # 方法2: 通过ss命令检测
    if command -v ss &> /dev/null && [[ ${#singbox_ports[@]} -eq 0 ]]; then
        local ss_ports
        ss_ports=$(ss -tlnp 2>/dev/null | grep sing-box | awk '{print $4}' | sed 's/.*://' | sort -n | uniq)
        if [[ -n "$ss_ports" ]]; then
            while IFS= read -r port; do
                [[ -n "$port" ]] && singbox_ports+=("$port")
            done <<< "$ss_ports"
        fi
    fi
    
    # 方法3: 通过配置文件分析
    local config_ports
    config_ports=$(detect_singbox_config_ports)
    if [[ -n "$config_ports" ]]; then
        while IFS= read -r port; do
            [[ -n "$port" ]] && singbox_ports+=("$port")
        done <<< "$config_ports"
    fi
    
    # 去重并排序
    if [[ ${#singbox_ports[@]} -gt 0 ]]; then
        printf '%s\n' "${singbox_ports[@]}" | sort -n | uniq
    fi
}

# 从sing-box配置文件检测端口
detect_singbox_config_ports() {
    local possible_configs=(
        "/usr/local/etc/sing-box/config.json"
        "/etc/sing-box/config.json"
        "/opt/sing-box/config.json"
        "/root/sing-box/config.json"
    )
    
    for config_file in "${possible_configs[@]}"; do
        if [[ -f "$config_file" ]]; then
            python3 << EOF 2>/dev/null || true
import json
try:
    with open('$config_file', 'r') as f:
        config = json.load(f)
    
    ports = set()
    for inbound in config.get('inbounds', []):
        port = inbound.get('listen_port')
        if port:
            ports.add(str(port))
    
    for port in sorted(ports, key=int):
        print(port)
except:
    pass
EOF
            break
        fi
    done
}

# 检测系统占用的端口
detect_system_ports() {
    info "检测系统占用的端口..."
    
    local used_ports=()
    
    # 检测TCP端口
    if command -v netstat &> /dev/null; then
        local tcp_ports
        tcp_ports=$(netstat -tln 2>/dev/null | awk 'NR>2 {print $4}' | sed 's/.*://' | sort -n | uniq)
        if [[ -n "$tcp_ports" ]]; then
            while IFS= read -r port; do
                [[ "$port" =~ ^[0-9]+$ ]] && used_ports+=("$port")
            done <<< "$tcp_ports"
        fi
    elif command -v ss &> /dev/null; then
        local tcp_ports
        tcp_ports=$(ss -tln 2>/dev/null | awk 'NR>1 {print $4}' | sed 's/.*://' | sort -n | uniq)
        if [[ -n "$tcp_ports" ]]; then
            while IFS= read -r port; do
                [[ "$port" =~ ^[0-9]+$ ]] && used_ports+=("$port")
            done <<< "$tcp_ports"
        fi
    fi
    
    # 去重并排序
    if [[ ${#used_ports[@]} -gt 0 ]]; then
        printf '%s\n' "${used_ports[@]}" | sort -n | uniq
    fi
}

# 推荐可用端口
recommend_port() {
    info "分析端口使用情况并推荐可用端口..."
    
    local singbox_ports
    singbox_ports=$(detect_singbox_ports)
    
    local system_ports
    system_ports=$(detect_system_ports)
    
    # 显示当前端口使用情况
    echo -e "\n${CYAN}=== 端口使用情况 ===${NC}"
    
    if [[ -n "$singbox_ports" ]]; then
        echo -e "${YELLOW}sing-box占用端口:${NC}"
        echo "$singbox_ports" | tr '\n' ' '
        echo
    else
        echo -e "${YELLOW}sing-box占用端口:${NC} 未检测到"
    fi
    
    # 推荐端口范围
    local recommended_ports=(2443 2083 3443 4443 5443 6443 7443 8443 9443 2080 3080 4080 5080)
    local available_ports=()
    
    for port in "${recommended_ports[@]}"; do
        if ! echo "$system_ports" | grep -q "^$port$"; then
            available_ports+=("$port")
        fi
    done
    
    echo -e "\n${GREEN}推荐可用端口:${NC}"
    if [[ ${#available_ports[@]} -gt 0 ]]; then
        printf '%s ' "${available_ports[@]}"
        echo
        echo -e "\n${BLUE}建议使用端口: ${available_ports[0]}${NC}"
        return 0
    else
        echo "未找到推荐端口范围内的可用端口"
        return 1
    fi
}

# 端口选择交互
select_port() {
    echo -e "\n${PURPLE}=== 端口选择 ===${NC}"
    
    # 显示端口使用情况和推荐
    recommend_port
    
    local recommended_ports=(2443 2083 3443 4443 5443 6443 7443 8443 9443 2080 3080 4080 5080)
    local system_ports
    system_ports=$(detect_system_ports)
    
    while true; do
        echo
        read -p "请选择Xray-core监听端口 (直接回车使用推荐端口 2443): " input_port
        
        # 默认使用2443
        if [[ -z "$input_port" ]]; then
            input_port=2443
        fi
        
        # 验证端口格式
        if ! [[ "$input_port" =~ ^[0-9]+$ ]] || [[ "$input_port" -lt 1 ]] || [[ "$input_port" -gt 65535 ]]; then
            error "请输入有效的端口号 (1-65535)"
            continue
        fi
        
        # 检查端口是否被占用
        if echo "$system_ports" | grep -q "^$input_port$"; then
            warning "端口 $input_port 已被占用，请选择其他端口"
            continue
        fi
        
        SELECTED_PORT="$input_port"
        success "已选择端口: $SELECTED_PORT"
        break
    done
}

# 收集SOCKS5代理信息
collect_socks5_info() {
    echo -e "\n${PURPLE}=== SOCKS5代理配置 ===${NC}"
    echo "请输入您的住宅SOCKS5代理信息："
    echo -e "${YELLOW}(直接回车使用默认配置: us.cliproxy.io:3010)${NC}"
    echo

    # 代理服务器地址
    read -p "代理服务器地址 (默认: us.cliproxy.io): " input_host
    if [[ -z "$input_host" ]]; then
        PROXY_HOST="us.cliproxy.io"
    else
        PROXY_HOST="$input_host"
    fi

    # 代理端口
    read -p "代理端口 (默认: 3010): " input_port
    if [[ -z "$input_port" ]]; then
        PROXY_PORT="3010"
    else
        if [[ "$input_port" =~ ^[0-9]+$ ]] && [[ "$input_port" -ge 1 ]] && [[ "$input_port" -le 65535 ]]; then
            PROXY_PORT="$input_port"
        else
            error "端口号无效，使用默认端口 3010"
            PROXY_PORT="3010"
        fi
    fi

    # 用户名
    read -p "用户名 (默认: jtf286099-region-US-sid-CaetFz3R-t-5): " input_user
    if [[ -z "$input_user" ]]; then
        PROXY_USER="jtf286099-region-US-sid-CaetFz3R-t-5"
    else
        PROXY_USER="$input_user"
    fi

    # 密码
    echo -n "密码 (默认: 3j2d9ov8): "
    read -s input_pass
    echo
    if [[ -z "$input_pass" ]]; then
        PROXY_PASS="3j2d9ov8"
    else
        PROXY_PASS="$input_pass"
    fi

    # 确认信息
    echo -e "\n${YELLOW}请确认SOCKS5代理信息:${NC}"
    echo "服务器: $PROXY_HOST"
    echo "端口: $PROXY_PORT"
    echo "用户名: $PROXY_USER"
    echo "密码: $(echo "$PROXY_PASS" | sed 's/./*/g')"
    echo

    while true; do
        read -p "信息是否正确? (y/n): " confirm
        case $confirm in
            [Yy]* ) break;;
            [Nn]* )
                echo "请重新输入信息"
                collect_socks5_info
                return
                ;;
            * ) echo "请输入 y 或 n";;
        esac
    done
}

# 检查Xray-core是否已安装
check_xray_installed() {
    if [[ -f "$XRAY_BINARY" ]] && command -v xray &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# 安装Xray-core
install_xray() {
    info "开始安装Xray-core..."

    # 创建目录
    mkdir -p /usr/local/etc/xray
    mkdir -p /var/log/xray

    # 下载安装脚本
    if command -v curl &> /dev/null; then
        curl -L https://github.com/XTLS/Xray-install/raw/main/install-release.sh -o /tmp/install-xray.sh
    elif command -v wget &> /dev/null; then
        wget https://github.com/XTLS/Xray-install/raw/main/install-release.sh -O /tmp/install-xray.sh
    else
        error "需要curl或wget来下载安装脚本"
        return 1
    fi

    # 执行安装
    chmod +x /tmp/install-xray.sh
    bash /tmp/install-xray.sh

    # 清理临时文件
    rm -f /tmp/install-xray.sh

    # 验证安装
    if check_xray_installed; then
        success "Xray-core安装成功"
        return 0
    else
        error "Xray-core安装失败"
        return 1
    fi
}

# 生成UUID
generate_uuid() {
    if command -v uuidgen &> /dev/null; then
        uuidgen
    elif [[ -f /proc/sys/kernel/random/uuid ]]; then
        cat /proc/sys/kernel/random/uuid
    else
        # 备用方法
        python3 -c "import uuid; print(uuid.uuid4())"
    fi
}

# 生成私钥
generate_private_key() {
    $XRAY_BINARY x25519
}

# 创建Xray-core配置文件
create_xray_config() {
    info "创建Xray-core配置文件..."

    local uuid
    uuid=$(generate_uuid)

    local keys
    keys=$(generate_private_key)
    local private_key
    private_key=$(echo "$keys" | grep "Private key:" | cut -d' ' -f3)
    local public_key
    public_key=$(echo "$keys" | grep "Public key:" | cut -d' ' -f3)

    # 获取伪装域名
    local dest_domain="www.bing.com"
    read -p "请输入伪装域名 (默认: $dest_domain): " input_domain
    if [[ -n "$input_domain" ]]; then
        dest_domain="$input_domain"
    fi

    # 创建配置文件
    cat > "$XRAY_CONFIG" << EOF
{
    "log": {
        "loglevel": "warning",
        "access": "/var/log/xray/access.log",
        "error": "/var/log/xray/error.log"
    },
    "inbounds": [
        {
            "port": $SELECTED_PORT,
            "protocol": "vless",
            "tag": "vless-reality",
            "settings": {
                "clients": [
                    {
                        "id": "$uuid",
                        "flow": "xtls-rprx-vision"
                    }
                ],
                "decryption": "none"
            },
            "streamSettings": {
                "network": "tcp",
                "security": "reality",
                "realitySettings": {
                    "show": false,
                    "dest": "$dest_domain:443",
                    "xver": 0,
                    "serverNames": [
                        "$dest_domain"
                    ],
                    "privateKey": "$private_key",
                    "shortIds": [
                        ""
                    ]
                }
            }
        }
    ],
    "outbounds": [
        {
            "protocol": "socks",
            "tag": "socks5-proxy",
            "settings": {
                "servers": [
                    {
                        "address": "$PROXY_HOST",
                        "port": $PROXY_PORT,
                        "users": [
                            {
                                "user": "$PROXY_USER",
                                "pass": "$PROXY_PASS"
                            }
                        ]
                    }
                ]
            }
        },
        {
            "protocol": "freedom",
            "tag": "direct"
        }
    ],
    "routing": {
        "rules": [
            {
                "inboundTag": ["vless-reality"],
                "outboundTag": "socks5-proxy"
            }
        ]
    }
}
EOF

    # 保存配置信息供后续使用
    echo "UUID: $uuid" > /tmp/xray_config_info.txt
    echo "Public Key: $public_key" >> /tmp/xray_config_info.txt
    echo "Port: $SELECTED_PORT" >> /tmp/xray_config_info.txt
    echo "Domain: $dest_domain" >> /tmp/xray_config_info.txt

    success "Xray-core配置文件创建成功"
}

# 创建systemd服务文件
create_xray_service() {
    info "创建Xray-core systemd服务..."

    # 确保日志目录存在并设置正确权限
    mkdir -p /var/log/xray
    chmod 755 /var/log/xray

    cat > "$XRAY_SERVICE" << EOF
[Unit]
Description=Xray Service
Documentation=https://github.com/xtls
After=network.target nss-lookup.target

[Service]
Type=simple
User=root
ExecStart=$XRAY_BINARY run -config $XRAY_CONFIG
Restart=on-failure
RestartPreventExitStatus=23
LimitNPROC=10000
LimitNOFILE=1000000

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    systemctl daemon-reload
    systemctl enable xray

    success "Xray-core服务创建成功"
}

# 测试SOCKS5代理连接
test_socks5_connection() {
    local proxy_host="$1"
    local proxy_port="$2"
    local proxy_user="$3"
    local proxy_pass="$4"

    info "测试SOCKS5代理连接..."

    # 使用curl测试SOCKS5连接
    if command -v curl &> /dev/null; then
        local test_result
        test_result=$(timeout 10 curl -s --socks5-hostname "${proxy_user}:${proxy_pass}@${proxy_host}:${proxy_port}" \
            --connect-timeout 5 \
            -w "%{http_code}" \
            -o /dev/null \
            "http://httpbin.org/ip" 2>/dev/null || echo "000")

        if [[ "$test_result" == "200" ]]; then
            success "SOCKS5代理连接测试成功"
            return 0
        else
            warning "SOCKS5代理连接测试失败 (HTTP状态码: $test_result)"
            warning "这可能影响流量转发，但Xray-core仍会尝试使用该代理"
            return 1
        fi
    else
        warning "未找到curl命令，跳过SOCKS5连接测试"
        return 0
    fi
}

# 检查并安装qrencode
check_and_install_qrencode() {
    if command -v qrencode &>/dev/null; then
        return 0
    fi

    info "检测到未安装qrencode，尝试自动安装..."

    # 检测系统包管理器并安装
    if command -v apt &>/dev/null; then
        if apt update >/dev/null 2>&1 && apt install -y qrencode >/dev/null 2>&1; then
            success "qrencode安装成功"
            return 0
        fi
    elif command -v yum &>/dev/null; then
        if yum install -y qrencode >/dev/null 2>&1; then
            success "qrencode安装成功"
            return 0
        fi
    elif command -v dnf &>/dev/null; then
        if dnf install -y qrencode >/dev/null 2>&1; then
            success "qrencode安装成功"
            return 0
        fi
    fi

    warning "qrencode安装失败，将无法显示二维码"
    info "您可以手动安装: apt install qrencode 或 yum install qrencode"
    return 1
}

# 从配置文件提取SOCKS5信息
extract_socks5_info() {
    if [[ ! -f "$XRAY_CONFIG" ]]; then
        return 1
    fi

    python3 << 'EOF'
import json
import sys
try:
    with open('/usr/local/etc/xray/config.json', 'r') as f:
        config = json.load(f)

    for outbound in config.get('outbounds', []):
        if outbound.get('protocol') == 'socks':
            servers = outbound.get('settings', {}).get('servers', [])
            if servers:
                server = servers[0]
                print(f"{server.get('address', '')}:{server.get('port', '')}")
                users = server.get('users', [])
                if users:
                    user = users[0]
                    print(f"{user.get('user', '')}:{user.get('pass', '')}")
                sys.exit(0)
    sys.exit(1)
except:
    sys.exit(1)
EOF
}

# 启动Xray-core服务
start_xray_service() {
    info "启动Xray-core服务 (包含SOCKS5代理配置)..."

    # 验证配置文件
    if ! $XRAY_BINARY -test -config "$XRAY_CONFIG"; then
        error "Xray-core配置文件验证失败"
        return 1
    fi

    # 提取并测试SOCKS5配置
    local socks5_info
    socks5_info=$(extract_socks5_info)
    if [[ $? -eq 0 && -n "$socks5_info" ]]; then
        local proxy_addr proxy_auth proxy_host proxy_port proxy_user proxy_pass
        proxy_addr=$(echo "$socks5_info" | head -1)
        proxy_auth=$(echo "$socks5_info" | tail -1)

        proxy_host=$(echo "$proxy_addr" | cut -d':' -f1)
        proxy_port=$(echo "$proxy_addr" | cut -d':' -f2)
        proxy_user=$(echo "$proxy_auth" | cut -d':' -f1)
        proxy_pass=$(echo "$proxy_auth" | cut -d':' -f2)

        # 测试SOCKS5连接
        test_socks5_connection "$proxy_host" "$proxy_port" "$proxy_user" "$proxy_pass"
    fi

    # 启动服务
    if systemctl start xray; then
        sleep 3
        if systemctl is-active --quiet xray; then
            success "Xray-core服务启动成功"
            info "SOCKS5代理配置已随Xray-core一起激活"
            return 0
        else
            error "Xray-core服务启动失败"
            return 1
        fi
    else
        error "Xray-core服务启动失败"
        return 1
    fi
}

# 停止Xray-core服务
stop_xray_service() {
    info "停止Xray-core服务 (同时停止SOCKS5代理转发)..."

    if systemctl stop xray; then
        success "Xray-core服务已停止"
        info "SOCKS5代理转发已随Xray-core一起停止"
        warning "注意: 配置文件保持不变，重新启动服务后配置将自动恢复"
        return 0
    else
        error "停止Xray-core服务失败"
        return 1
    fi
}

# 显示客户端配置信息
show_client_config() {
    if [[ ! -f /tmp/xray_config_info.txt ]]; then
        # 尝试从配置文件中提取信息
        if [[ -f "$XRAY_CONFIG" ]]; then
            extract_config_info
        else
            error "未找到配置信息"
            return 1
        fi
    fi

    local uuid port domain public_key
    uuid=$(grep "UUID:" /tmp/xray_config_info.txt | cut -d' ' -f2)
    port=$(grep "Port:" /tmp/xray_config_info.txt | cut -d' ' -f2)
    domain=$(grep "Domain:" /tmp/xray_config_info.txt | cut -d' ' -f2)
    public_key=$(grep "Public Key:" /tmp/xray_config_info.txt | cut -d' ' -f3)

    # 获取服务器IP
    local server_ip
    server_ip=$(curl -s ipv4.icanhazip.com 2>/dev/null || curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP")

    # 检查并安装qrencode
    local qrencode_available=false
    if check_and_install_qrencode; then
        qrencode_available=true
    fi

    echo -e "\n${CYAN}=== Xray-core VLESS + Reality 客户端配置 ===${NC}"
    echo -e "${GREEN}服务器地址:${NC} $server_ip"
    echo -e "${GREEN}端口:${NC} $port"
    echo -e "${GREEN}用户ID (UUID):${NC} $uuid"
    echo -e "${GREEN}流控:${NC} xtls-rprx-vision"
    echo -e "${GREEN}传输协议:${NC} tcp"
    echo -e "${GREEN}传输层安全:${NC} reality"
    echo -e "${GREEN}SNI:${NC} $domain"
    echo -e "${GREEN}Fingerprint:${NC} chrome"
    echo -e "${GREEN}PublicKey:${NC} $public_key"
    echo -e "${GREEN}ShortId:${NC} (留空)"
    echo -e "${GREEN}SpiderX:${NC} /"
    echo

    # 生成VLESS链接
    local vless_link="vless://${uuid}@${server_ip}:${port}?security=reality&sni=${domain}&fp=chrome&pbk=${public_key}&sid=&flow=xtls-rprx-vision&type=tcp#Xray-Reality-${server_ip}-$(date +%s)"

    echo -e "${CYAN}VLESS Reality 导入链接:${NC}"
    echo -e "${GREEN}${vless_link}${NC}"
    echo

    # 显示二维码
    if [[ "$qrencode_available" == "true" ]] && command -v qrencode &>/dev/null; then
        echo -e "${CYAN}VLESS Reality 二维码:${NC}"
        qrencode -t ANSIUTF8 "${vless_link}"
        echo
    fi

    # 获取SOCKS5信息并显示
    local socks5_info
    socks5_info=$(extract_socks5_info)
    if [[ $? -eq 0 && -n "$socks5_info" ]]; then
        local proxy_addr proxy_auth proxy_host proxy_port
        proxy_addr=$(echo "$socks5_info" | head -1)
        proxy_auth=$(echo "$socks5_info" | tail -1)
        proxy_host=$(echo "$proxy_addr" | cut -d':' -f1)
        proxy_port=$(echo "$proxy_addr" | cut -d':' -f2)

        echo -e "${YELLOW}注意: 此配置的流量将通过SOCKS5代理(${proxy_host}:${proxy_port})转发到住宅IP${NC}"
    else
        echo -e "${YELLOW}注意: 此配置使用直连模式${NC}"
    fi
    echo
}

# 从配置文件提取信息
extract_config_info() {
    if [[ ! -f "$XRAY_CONFIG" ]]; then
        return 1
    fi

    python3 << EOF > /tmp/xray_config_info.txt
import json
try:
    with open('$XRAY_CONFIG', 'r') as f:
        config = json.load(f)

    # 提取入站配置
    for inbound in config.get('inbounds', []):
        if inbound.get('protocol') == 'vless':
            port = inbound.get('port', 'unknown')
            clients = inbound.get('settings', {}).get('clients', [])
            if clients:
                uuid = clients[0].get('id', 'unknown')

            reality_settings = inbound.get('streamSettings', {}).get('realitySettings', {})
            dest = reality_settings.get('dest', 'unknown:443')
            domain = dest.split(':')[0]
            private_key = reality_settings.get('privateKey', 'unknown')

            print(f"UUID: {uuid}")
            print(f"Port: {port}")
            print(f"Domain: {domain}")
            print(f"Private Key: {private_key}")
            break
except Exception as e:
    print(f"Error: {e}")
EOF

    # 从私钥计算公钥
    local private_key
    private_key=$(grep "Private Key:" /tmp/xray_config_info.txt | cut -d' ' -f3)

    if [[ -n "$private_key" && "$private_key" != "unknown" ]]; then
        # 使用xray工具从私钥计算公钥
        local public_key
        public_key=$(echo "$private_key" | xray x25519 2>/dev/null | grep "Public key:" | awk '{print $3}')

        # 如果仍然失败
        if [[ -z "$public_key" ]]; then
            warning "无法从私钥计算公钥，可能需要重新生成配置"
            public_key="INVALID_PUBLIC_KEY_PLEASE_REINSTALL"
        fi

        # 更新配置信息文件，添加公钥
        echo "Public Key: $public_key" >> /tmp/xray_config_info.txt
    else
        echo "Public Key: INVALID_PRIVATE_KEY" >> /tmp/xray_config_info.txt
    fi
}

# 显示服务状态
show_status() {
    echo -e "\n${CYAN}=== Xray-core 服务状态 ===${NC}"

    # 检查Xray-core是否安装
    if check_xray_installed; then
        echo -e "${GREEN}Xray-core:${NC} 已安装"
        echo -e "${GREEN}版本:${NC} $($XRAY_BINARY version | head -1)"
    else
        echo -e "${RED}Xray-core:${NC} 未安装"
        return 1
    fi

    # 检查配置文件
    if [[ -f "$XRAY_CONFIG" ]]; then
        echo -e "${GREEN}配置文件:${NC} 存在 ($XRAY_CONFIG)"

        # 显示SOCKS5配置状态
        local socks5_info
        socks5_info=$(extract_socks5_info)
        if [[ $? -eq 0 && -n "$socks5_info" ]]; then
            local proxy_addr
            proxy_addr=$(echo "$socks5_info" | head -1)
            echo -e "${GREEN}SOCKS5代理:${NC} 已配置 ($proxy_addr)"
        else
            echo -e "${YELLOW}SOCKS5代理:${NC} 未配置"
        fi
    else
        echo -e "${RED}配置文件:${NC} 不存在"
        return 1
    fi

    # 检查服务状态
    if systemctl is-active --quiet xray; then
        echo -e "${GREEN}服务状态:${NC} 运行中"
        echo -e "${GREEN}SOCKS5转发:${NC} 活跃 (随Xray-core运行)"

        # 如果服务运行中，测试SOCKS5连接
        local socks5_info
        socks5_info=$(extract_socks5_info)
        if [[ $? -eq 0 && -n "$socks5_info" ]]; then
            local proxy_addr proxy_auth proxy_host proxy_port proxy_user proxy_pass
            proxy_addr=$(echo "$socks5_info" | head -1)
            proxy_auth=$(echo "$socks5_info" | tail -1)

            proxy_host=$(echo "$proxy_addr" | cut -d':' -f1)
            proxy_port=$(echo "$proxy_addr" | cut -d':' -f2)
            proxy_user=$(echo "$proxy_auth" | cut -d':' -f1)
            proxy_pass=$(echo "$proxy_auth" | cut -d':' -f2)

            if test_socks5_connection "$proxy_host" "$proxy_port" "$proxy_user" "$proxy_pass" >/dev/null 2>&1; then
                echo -e "${GREEN}SOCKS5连接:${NC} 正常"
            else
                echo -e "${YELLOW}SOCKS5连接:${NC} 异常 (可能影响流量转发)"
            fi
        fi
    else
        echo -e "${RED}服务状态:${NC} 未运行"
        echo -e "${RED}SOCKS5转发:${NC} 未活跃"
    fi

    # 检查端口监听
    local listening_port
    listening_port=$(netstat -tlnp 2>/dev/null | grep xray | awk '{print $4}' | sed 's/.*://' | head -1)
    if [[ -n "$listening_port" ]]; then
        echo -e "${GREEN}监听端口:${NC} $listening_port"
    else
        echo -e "${YELLOW}监听端口:${NC} 未检测到"
    fi

    echo -e "\n${CYAN}=== 配置持久性说明 ===${NC}"
    echo -e "${YELLOW}• 配置文件在服务重启后保持不变${NC}"
    echo -e "${YELLOW}• 客户端VPN软件无需重新加载配置${NC}"
    echo -e "${YELLOW}• SOCKS5代理设置随Xray-core自动启停${NC}"
    echo
}

# 完整安装流程
install_and_configure() {
    echo -e "\n${PURPLE}=== 开始安装和配置Xray-core ===${NC}"

    # 检查是否已安装
    if check_xray_installed; then
        warning "Xray-core已安装，是否重新配置?"
        read -p "继续配置? (y/n): " confirm
        case $confirm in
            [Yy]* ) ;;
            * )
                echo "操作已取消"
                return 0
                ;;
        esac
    else
        # 安装Xray-core
        if ! install_xray; then
            error "Xray-core安装失败"
            return 1
        fi
    fi

    # 选择端口
    select_port

    # 收集SOCKS5信息
    collect_socks5_info

    # 创建配置文件
    create_xray_config

    # 创建服务文件
    create_xray_service

    # 启动服务
    if start_xray_service; then
        success "Xray-core安装和配置完成!"
        show_client_config
    else
        error "服务启动失败，请检查配置"
        return 1
    fi
}

# 卸载Xray-core
uninstall_xray() {
    echo -e "\n${RED}=== 卸载Xray-core ===${NC}"
    warning "这将完全删除Xray-core及其配置文件"
    read -p "确认卸载? (y/n): " confirm
    case $confirm in
        [Yy]* ) ;;
        * )
            echo "操作已取消"
            return 0
            ;;
    esac

    # 停止服务
    if systemctl is-active --quiet xray; then
        systemctl stop xray
    fi

    # 禁用服务
    if systemctl is-enabled --quiet xray; then
        systemctl disable xray
    fi

    # 删除服务文件
    if [[ -f "$XRAY_SERVICE" ]]; then
        rm -f "$XRAY_SERVICE"
        systemctl daemon-reload
    fi

    # 删除配置文件
    if [[ -f "$XRAY_CONFIG" ]]; then
        rm -f "$XRAY_CONFIG"
    fi

    # 删除日志目录
    if [[ -d "/var/log/xray" ]]; then
        rm -rf "/var/log/xray"
    fi

    # 删除二进制文件
    if [[ -f "$XRAY_BINARY" ]]; then
        rm -f "$XRAY_BINARY"
    fi

    # 删除配置目录
    if [[ -d "/usr/local/etc/xray" ]]; then
        rm -rf "/usr/local/etc/xray"
    fi

    # 清理临时文件
    rm -f /tmp/xray_config_info.txt

    success "Xray-core已完全卸载"
}

# 安装VLESS + Reality协议（直连模式）
install_direct_mode() {
    echo -e "\n${CYAN}=== 安装VLESS + Reality协议（直连模式，不使用SOCKS5） ===${NC}"

    # 检查并安装Xray-core
    if ! command -v xray &> /dev/null; then
        info "正在安装Xray-core..."
        install_xray_core
    else
        info "Xray-core已安装，版本: $(xray version | head -1)"
    fi

    # 获取服务器IP
    SERVER_IP=$(curl -s ipv4.icanhazip.com 2>/dev/null || curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP")
    if [[ -z "$SERVER_IP" || "$SERVER_IP" == "YOUR_SERVER_IP" ]]; then
        error "无法获取服务器IP地址"
        return 1
    fi

    # 用户自定义端口
    echo -e "\n${YELLOW}=== 端口配置 ===${NC}"
    local system_ports
    system_ports=$(netstat -tlnp 2>/dev/null | awk '{print $4}' | sed 's/.*://' | sort -n | uniq)

    # 推荐端口范围
    local recommended_ports=(2443 2083 3443 4443 5443 6443 7443 8443 9443 2080 3080 4080 5080)
    local available_ports=()

    for port in "${recommended_ports[@]}"; do
        if ! echo "$system_ports" | grep -q "^$port$"; then
            available_ports+=("$port")
        fi
    done

    echo -e "${GREEN}推荐可用端口:${NC}"
    if [[ ${#available_ports[@]} -gt 0 ]]; then
        printf '%s ' "${available_ports[@]}"
        echo
        echo -e "\n${BLUE}建议使用端口: ${available_ports[0]}${NC}"
    else
        echo "未找到推荐端口范围内的可用端口"
    fi

    while true; do
        read -p "请输入要使用的端口 (直接回车使用推荐端口 ${available_ports[0]}): " input_port

        if [[ -z "$input_port" ]]; then
            if [[ ${#available_ports[@]} -gt 0 ]]; then
                VLESS_PORT=${available_ports[0]}
                break
            else
                error "没有推荐端口可用，请手动输入端口"
                continue
            fi
        fi

        # 验证端口格式
        if ! [[ "$input_port" =~ ^[0-9]+$ ]] || [[ "$input_port" -lt 1 ]] || [[ "$input_port" -gt 65535 ]]; then
            error "端口必须是1-65535之间的数字"
            continue
        fi

        # 检查端口是否被占用
        if echo "$system_ports" | grep -q "^$input_port$"; then
            error "端口 $input_port 已被占用，请选择其他端口"
            continue
        fi

        VLESS_PORT=$input_port
        break
    done

    success "将使用端口: $VLESS_PORT"

    # 用户自定义SNI伪装域名
    echo -e "\n${YELLOW}=== SNI伪装域名配置 ===${NC}"
    local recommended_domains=("www.bing.com" "www.yahoo.com" "www.cloudflare.com" "www.microsoft.com" "www.apple.com")

    echo -e "${GREEN}推荐伪装域名:${NC}"
    for i in "${!recommended_domains[@]}"; do
        echo "$((i+1)). ${recommended_domains[i]}"
    done

    while true; do
        read -p "请选择伪装域名 (1-${#recommended_domains[@]}) 或输入自定义域名 (直接回车使用 www.bing.com): " domain_choice

        if [[ -z "$domain_choice" ]]; then
            SNI_DOMAIN="www.bing.com"
            break
        elif [[ "$domain_choice" =~ ^[1-5]$ ]]; then
            SNI_DOMAIN=${recommended_domains[$((domain_choice-1))]}
            break
        elif [[ "$domain_choice" =~ ^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
            SNI_DOMAIN="$domain_choice"
            break
        else
            error "请输入有效的选项 (1-${#recommended_domains[@]}) 或有效的域名"
        fi
    done

    success "将使用伪装域名: $SNI_DOMAIN"

    # 生成UUID和密钥
    UUID=$(xray uuid)
    KEYS=$(xray x25519)
    PRIVATE_KEY=$(echo "$KEYS" | grep "Private key:" | awk '{print $3}')
    PUBLIC_KEY=$(echo "$KEYS" | grep "Public key:" | awk '{print $3}')

    # 创建配置目录
    mkdir -p /usr/local/etc/xray
    mkdir -p /var/log/xray

    # 生成直连模式配置
    generate_direct_config "$VLESS_PORT" "$UUID" "$PRIVATE_KEY" "$SNI_DOMAIN"

    # 验证配置
    if ! xray -test -config "$XRAY_CONFIG"; then
        error "配置文件验证失败"
        return 1
    fi

    # 创建systemd服务文件
    info "创建Xray-core systemd服务..."
    mkdir -p /var/log/xray
    chmod 755 /var/log/xray

    cat > "$XRAY_SERVICE" << EOF
[Unit]
Description=Xray Service
Documentation=https://github.com/xtls
After=network.target nss-lookup.target

[Service]
Type=simple
User=root
ExecStart=$XRAY_BINARY run -config $XRAY_CONFIG
Restart=on-failure
RestartPreventExitStatus=23
LimitNPROC=10000
LimitNOFILE=1000000

[Install]
WantedBy=multi-user.target
EOF

    # 启动服务
    systemctl daemon-reload
    systemctl enable xray
    systemctl start xray

    # 检查服务状态
    if systemctl is-active --quiet xray; then
        success "Xray-core服务启动成功"

        # 保存配置信息
        echo "UUID: $UUID" > /tmp/xray_config_info.txt
        echo "Port: $VLESS_PORT" >> /tmp/xray_config_info.txt
        echo "Domain: $SNI_DOMAIN" >> /tmp/xray_config_info.txt
        echo "Private Key: $PRIVATE_KEY" >> /tmp/xray_config_info.txt
        echo "Public Key: $PUBLIC_KEY" >> /tmp/xray_config_info.txt

        # 显示配置信息
        echo -e "\n${GREEN}=== 直连模式配置完成 ===${NC}"
        echo -e "${YELLOW}服务器地址:${NC} $SERVER_IP"
        echo -e "${YELLOW}端口:${NC} $VLESS_PORT"
        echo -e "${YELLOW}UUID:${NC} $UUID"
        echo -e "${YELLOW}公钥:${NC} $PUBLIC_KEY"
        echo -e "${YELLOW}SNI伪装域名:${NC} $SNI_DOMAIN"
        echo -e "${YELLOW}模式:${NC} 直连（不使用SOCKS5代理）"

        # 生成客户端链接
        VLESS_LINK="vless://${UUID}@${SERVER_IP}:${VLESS_PORT}?security=reality&sni=${SNI_DOMAIN}&fp=chrome&pbk=${PUBLIC_KEY}&sid=&flow=xtls-rprx-vision&type=tcp#Xray-Direct-${SERVER_IP}-$(date +%s)"
        echo -e "\n${CYAN}VLESS Reality 导入链接:${NC}"
        echo "$VLESS_LINK"

        # 生成二维码
        if check_and_install_qrencode; then
            echo -e "\n${CYAN}VLESS Reality 二维码:${NC}"
            qrencode -t ANSIUTF8 "$VLESS_LINK"
        fi

    else
        error "Xray-core服务启动失败"
        systemctl status xray
        return 1
    fi
}

# 生成直连模式配置文件
generate_direct_config() {
    local port=$1
    local uuid=$2
    local private_key=$3
    local sni_domain=$4

    cat > "$XRAY_CONFIG" << EOF
{
    "log": {
        "loglevel": "warning",
        "access": "/var/log/xray/access.log",
        "error": "/var/log/xray/error.log"
    },
    "dns": {
        "servers": [
            "*******",
            "*******",
            "localhost"
        ]
    },
    "inbounds": [
        {
            "port": $port,
            "protocol": "vless",
            "tag": "vless-reality",
            "settings": {
                "clients": [
                    {
                        "id": "$uuid",
                        "flow": "xtls-rprx-vision"
                    }
                ],
                "decryption": "none"
            },
            "streamSettings": {
                "network": "tcp",
                "security": "reality",
                "realitySettings": {
                    "show": false,
                    "dest": "${sni_domain}:443",
                    "xver": 0,
                    "serverNames": [
                        "${sni_domain}"
                    ],
                    "privateKey": "$private_key",
                    "shortIds": [
                        ""
                    ]
                }
            }
        }
    ],
    "outbounds": [
        {
            "protocol": "freedom",
            "tag": "direct"
        },
        {
            "protocol": "blackhole",
            "tag": "block"
        }
    ],
    "routing": {
        "domainStrategy": "AsIs",
        "rules": [
            {
                "type": "field",
                "protocol": ["bittorrent"],
                "outboundTag": "direct"
            }
        ]
    }
}
EOF
}

# 显示主菜单
show_menu() {
    clear
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                    Xray-core 管理脚本                        ║${NC}"
    echo -e "${CYAN}║                VLESS + Reality + SOCKS5                      ║${NC}"
    echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  ${GREEN}1.${NC} 安装/重装 VLESS + Reality 协议并绑定SOCKS5          ║"
    echo -e "${CYAN}║  ${GREEN}2.${NC} 查看当前运行的 VLESS + Reality 协议配置             ║"
    echo -e "${CYAN}║  ${GREEN}3.${NC} 停止 Xray-core 服务 (同时停止SOCKS5转发)           ║"
    echo -e "${CYAN}║  ${GREEN}4.${NC} 启动 Xray-core 服务 (同时启动SOCKS5转发)           ║"
    echo -e "${CYAN}║  ${GREEN}5.${NC} 重启 Xray-core 服务 (配置保持不变)                 ║"
    echo -e "${CYAN}║  ${GREEN}6.${NC} 查看服务状态                                       ║"
    echo -e "${CYAN}║  ${GREEN}7.${NC} 显示客户端配置                                     ║"
    echo -e "${CYAN}║  ${GREEN}8.${NC} 完全卸载 Xray-core                                 ║"
    echo -e "${CYAN}║  ${GREEN}9.${NC} 安装VLESS + Reality协议（直连模式，不使用SOCKS5）  ║"
    echo -e "${CYAN}║ ${GREEN}10.${NC} 安装Trojan2025协议（直连模式，不使用SOCKS5）       ║"
    echo -e "${CYAN}║  ${GREEN}0.${NC} 退出                                               ║"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 主函数
main() {
    # 创建日志文件
    touch "$SCRIPT_LOG"

    # 检查root权限
    check_root

    while true; do
        show_menu
        read -p "请选择操作 [0-10]: " choice

        case $choice in
            1)
                install_and_configure
                read -p "按回车键继续..."
                ;;
            2)
                if [[ -f "$XRAY_CONFIG" ]]; then
                    show_client_config
                else
                    error "未找到Xray-core配置文件"
                fi
                read -p "按回车键继续..."
                ;;
            3)
                stop_xray_service
                read -p "按回车键继续..."
                ;;
            4)
                if [[ -f "$XRAY_CONFIG" ]]; then
                    start_xray_service
                else
                    error "配置文件不存在，请先安装配置"
                fi
                read -p "按回车键继续..."
                ;;
            5)
                if [[ -f "$XRAY_CONFIG" ]]; then
                    stop_xray_service
                    sleep 2
                    start_xray_service
                else
                    error "配置文件不存在，请先安装配置"
                fi
                read -p "按回车键继续..."
                ;;
            6)
                show_status
                read -p "按回车键继续..."
                ;;
            7)
                show_client_config
                read -p "按回车键继续..."
                ;;
            8)
                uninstall_xray
                read -p "按回车键继续..."
                ;;
            9)
                install_direct_mode
                read -p "按回车键继续..."
                ;;
            10)
                install_trojan2025_mode
                read -p "按回车键继续..."
                ;;
            0)
                echo -e "${GREEN}感谢使用 Xray-core 管理脚本！${NC}"
                exit 0
                ;;
            *)
                error "无效选择，请输入 0-10"
                read -p "按回车键继续..."
                ;;
        esac
    done
}

# 安装Trojan2025协议（直连模式）
install_trojan2025_mode() {
    echo -e "\n${CYAN}=== 安装Trojan2025协议（直连模式，不使用SOCKS5） ===${NC}"

    # 检查并安装Xray-core
    if ! command -v xray &> /dev/null; then
        info "正在安装Xray-core..."
        install_xray_core
    else
        info "Xray-core已安装，版本: $(xray version | head -1)"
    fi

    # 获取服务器IP
    SERVER_IP=$(curl -s ipv4.icanhazip.com 2>/dev/null || curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP")
    if [[ -z "$SERVER_IP" || "$SERVER_IP" == "YOUR_SERVER_IP" ]]; then
        error "无法获取服务器IP地址"
        return 1
    fi

    # 用户自定义端口
    echo -e "\n${YELLOW}=== 端口配置 ===${NC}"
    local system_ports
    system_ports=$(netstat -tlnp 2>/dev/null | awk '{print $4}' | sed 's/.*://' | sort -n | uniq)

    # 推荐端口范围
    local recommended_ports=(443 8443 2443 3443 4443 5443 6443 7443 9443 2080 3080 4080 5080)
    local available_ports=()

    for port in "${recommended_ports[@]}"; do
        if ! echo "$system_ports" | grep -q "^$port$"; then
            available_ports+=("$port")
        fi
    done

    echo -e "${GREEN}推荐可用端口:${NC}"
    if [[ ${#available_ports[@]} -gt 0 ]]; then
        printf '%s ' "${available_ports[@]}"
        echo
        echo -e "\n${BLUE}建议使用端口: ${available_ports[0]}${NC}"
    else
        echo "未找到推荐端口范围内的可用端口"
    fi

    while true; do
        read -p "请输入要使用的端口 (直接回车使用推荐端口 ${available_ports[0]}): " input_port

        if [[ -z "$input_port" ]]; then
            if [[ ${#available_ports[@]} -gt 0 ]]; then
                TROJAN_PORT=${available_ports[0]}
                break
            else
                error "没有推荐端口可用，请手动输入端口"
                continue
            fi
        fi

        # 验证端口格式
        if ! [[ "$input_port" =~ ^[0-9]+$ ]] || [[ "$input_port" -lt 1 ]] || [[ "$input_port" -gt 65535 ]]; then
            error "端口必须是1-65535之间的数字"
            continue
        fi

        # 检查端口是否被占用
        if echo "$system_ports" | grep -q "^$input_port$"; then
            error "端口 $input_port 已被占用，请选择其他端口"
            continue
        fi

        TROJAN_PORT=$input_port
        break
    done

    success "将使用端口: $TROJAN_PORT"

    # 用户自定义伪装域名
    echo -e "\n${YELLOW}=== 伪装域名配置 ===${NC}"
    local recommended_domains=("www.bing.com" "www.yahoo.com" "www.cloudflare.com" "www.microsoft.com" "www.apple.com")

    echo -e "${GREEN}推荐伪装域名:${NC}"
    for i in "${!recommended_domains[@]}"; do
        echo "$((i+1)). ${recommended_domains[i]}"
    done

    while true; do
        read -p "请选择伪装域名 (1-${#recommended_domains[@]}) 或输入自定义域名 (直接回车使用 www.bing.com): " domain_choice

        if [[ -z "$domain_choice" ]]; then
            FALLBACK_DOMAIN="www.bing.com"
            break
        elif [[ "$domain_choice" =~ ^[1-5]$ ]]; then
            FALLBACK_DOMAIN=${recommended_domains[$((domain_choice-1))]}
            break
        elif [[ "$domain_choice" =~ ^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
            FALLBACK_DOMAIN="$domain_choice"
            break
        else
            error "请输入有效的选项 (1-${#recommended_domains[@]}) 或有效的域名"
        fi
    done

    success "将使用伪装域名: $FALLBACK_DOMAIN"

    # 生成密码
    TROJAN_PASSWORD=$(openssl rand -hex 16)

    # 生成trojan2025配置
    generate_trojan2025_config "$TROJAN_PORT" "$TROJAN_PASSWORD" "$FALLBACK_DOMAIN"

    # 验证配置
    if xray -test -config "$XRAY_CONFIG" > /dev/null 2>&1; then
        success "配置文件验证通过"
    else
        error "配置文件验证失败"
        return 1
    fi

    # 创建systemd服务文件
    info "创建Xray-core systemd服务..."
    mkdir -p /var/log/xray
    chmod 755 /var/log/xray

    cat > "$XRAY_SERVICE" << EOF
[Unit]
Description=Xray Service
Documentation=https://github.com/xtls
After=network.target nss-lookup.target

[Service]
User=root
CapabilityBoundingSet=CAP_NET_ADMIN CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_ADMIN CAP_NET_BIND_SERVICE
NoNewPrivileges=true
ExecStart=/usr/local/bin/xray run -config /usr/local/etc/xray/config.json
Restart=on-failure
RestartPreventExitStatus=23
LimitNPROC=10000
LimitNOFILE=1000000

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd并启动服务
    systemctl daemon-reload
    systemctl enable xray
    systemctl start xray

    if systemctl is-active --quiet xray; then
        success "Xray-core服务启动成功"

            # 保存配置信息
            echo "Protocol: Trojan2025" > /tmp/xray_config_info.txt
            echo "Port: $TROJAN_PORT" >> /tmp/xray_config_info.txt
            echo "Password: $TROJAN_PASSWORD" >> /tmp/xray_config_info.txt
            echo "Domain: $FALLBACK_DOMAIN" >> /tmp/xray_config_info.txt

            # 显示配置信息
            echo -e "\n${GREEN}=== Trojan2025直连模式配置完成 ===${NC}"
            echo -e "${YELLOW}服务器地址:${NC} $SERVER_IP"
            echo -e "${YELLOW}端口:${NC} $TROJAN_PORT"
            echo -e "${YELLOW}密码:${NC} $TROJAN_PASSWORD"
            echo -e "${YELLOW}伪装域名:${NC} $FALLBACK_DOMAIN"
            echo -e "${YELLOW}模式:${NC} 直连（不使用SOCKS5代理）"

            # 生成客户端链接
            TROJAN_LINK="trojan://${TROJAN_PASSWORD}@${SERVER_IP}:${TROJAN_PORT}?security=tls&sni=${FALLBACK_DOMAIN}&type=tcp#Trojan2025-Direct-${SERVER_IP}-$(date +%s)"

            echo -e "\n${GREEN}Trojan2025 导入链接:${NC}"
            echo "$TROJAN_LINK"

            # 生成二维码
            if check_and_install_qrencode; then
                echo -e "\n${GREEN}Trojan2025 二维码:${NC}"
                qrencode -t ANSIUTF8 "$TROJAN_LINK"
            fi

    else
        error "Xray-core服务启动失败"
        return 1
    fi
}

# 生成Trojan2025配置文件
generate_trojan2025_config() {
    local port=$1
    local password=$2
    local fallback_domain=$3

    cat > "$XRAY_CONFIG" << EOF
{
    "log": {
        "loglevel": "warning",
        "access": "/var/log/xray/access.log",
        "error": "/var/log/xray/error.log"
    },
    "dns": {
        "servers": [
            "*******",
            "*******",
            "localhost"
        ]
    },
    "inbounds": [
        {
            "port": $port,
            "protocol": "trojan",
            "tag": "trojan-in",
            "settings": {
                "clients": [
                    {
                        "password": "$password"
                    }
                ],
                "fallbacks": [
                    {
                        "dest": "$fallback_domain:443"
                    }
                ]
            },
            "streamSettings": {
                "network": "tcp",
                "security": "tls",
                "tlsSettings": {
                    "certificates": [
                        {
                            "usage": "encipherment",
                            "certificateFile": "/usr/local/etc/xray/cert.pem",
                            "keyFile": "/usr/local/etc/xray/key.pem"
                        }
                    ]
                }
            }
        }
    ],
    "outbounds": [
        {
            "protocol": "freedom",
            "tag": "direct"
        },
        {
            "protocol": "blackhole",
            "tag": "block"
        }
    ],
    "routing": {
        "domainStrategy": "AsIs",
        "rules": [
            {
                "type": "field",
                "protocol": ["bittorrent"],
                "outboundTag": "direct"
            }
        ]
    }
}
EOF

    # 生成自签名证书
    generate_self_signed_cert "$fallback_domain"
}

# 生成自签名证书
generate_self_signed_cert() {
    local domain=$1
    local cert_dir="/usr/local/etc/xray"

    info "正在生成自签名证书..."

    # 创建证书目录
    mkdir -p "$cert_dir"

    # 生成私钥
    openssl genrsa -out "$cert_dir/key.pem" 2048

    # 生成证书
    openssl req -new -x509 -key "$cert_dir/key.pem" -out "$cert_dir/cert.pem" -days 365 -subj "/CN=$domain"

    # 设置权限
    chmod 600 "$cert_dir/key.pem"
    chmod 644 "$cert_dir/cert.pem"

    success "自签名证书生成完成"
}

# 运行主函数
main "$@"
