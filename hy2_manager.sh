#!/bin/bash

# Hysteria 2 代理服务交互式管理工具
# 支持静态住宅IP和动态IP配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 配置变量
WORK_DIR="/etc/hysteria2"
CERT_FILE="$WORK_DIR/cert.pem"
KEY_FILE="$WORK_DIR/private.key"
SOCKS5_CONFIG="$WORK_DIR/config-socks5.yaml"
SSH_CONFIG="$WORK_DIR/config-ssh.yaml"

# 代理配置
SSH_PROXY="ssh://*************:43001:ANo3v2iTBI1HfgLe7b:yHB9v0LDXZwTruJ1nY"
SOCKS5_PROXY="socks5://gate-us.ipfoxy.io:58688:customer-Q0SXajDpWx-cc-US-st-NewYork-city-NewYork-sessid-1754480265_10000:vUguohGcRZK1S0s"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${CYAN}           Hysteria 2 代理服务交互式管理工具${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${WHITE}支持功能：${NC}"
    echo -e "${YELLOW}  • 静态住宅IP代理 (SSH隧道)${NC}"
    echo -e "${YELLOW}  • 动态IP代理 (SOCKS5)${NC}"
    echo -e "${YELLOW}  • 服务状态监控${NC}"
    echo -e "${YELLOW}  • 配置管理${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
}

# 显示主菜单
show_menu() {
    echo -e "${PURPLE}请选择操作:${NC}"
    echo -e "${YELLOW}1.${NC} 测试现有服务状态"
    echo -e "${YELLOW}2.${NC} 配置静态住宅IP代理服务 (SSH)"
    echo -e "${YELLOW}3.${NC} 配置动态IP代理服务 (SOCKS5)"
    echo -e "${YELLOW}4.${NC} 显示现有协议服务配置信息"
    echo -e "${YELLOW}5.${NC} 清理所有Hysteria 2服务"
    echo -e "${YELLOW}0.${NC} 退出"
    echo ""
    echo -n -e "${CYAN}请输入选项 [0-5]: ${NC}"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    # 检查Hysteria 2
    if ! command -v /usr/local/bin/hysteria >/dev/null 2>&1; then
        missing_deps+=("hysteria")
    fi
    
    # 检查其他依赖
    for cmd in curl netstat openssl sshpass; do
        if ! command -v $cmd >/dev/null 2>&1; then
            missing_deps+=("$cmd")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        log_info "正在安装缺少的依赖..."
        apt update -qq
        for dep in "${missing_deps[@]}"; do
            if [[ "$dep" != "hysteria" ]]; then
                apt install -y "$dep" >/dev/null 2>&1
            fi
        done
        
        if [[ " ${missing_deps[*]} " =~ " hysteria " ]]; then
            log_error "Hysteria 2未安装，请先安装Hysteria 2"
            return 1
        fi
    fi
    
    return 0
}

# 获取服务器IP
get_server_ip() {
    local ip=$(curl -s --max-time 10 ifconfig.me 2>/dev/null || curl -s --max-time 10 ipinfo.io/ip 2>/dev/null || curl -s --max-time 10 icanhazip.com 2>/dev/null)
    if [[ -z "$ip" ]]; then
        log_error "无法获取服务器IP地址"
        return 1
    fi
    echo "$ip"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tulpn 2>/dev/null | grep -q ":$port "; then
        return 1
    fi
    return 0
}

# 生成随机密码
generate_password() {
    openssl rand -hex 16
}

# 创建工作目录和证书
setup_environment() {
    log_info "设置工作环境..."
    
    # 创建工作目录
    mkdir -p "$WORK_DIR"
    
    # 生成TLS证书
    if [[ ! -f "$CERT_FILE" || ! -f "$KEY_FILE" ]]; then
        log_info "生成TLS证书..."
        openssl genrsa -out "$KEY_FILE" 2048 2>/dev/null
        openssl req -new -x509 -key "$KEY_FILE" -out "$CERT_FILE" -days 365 -subj "/CN=bing.com" 2>/dev/null
        chmod 644 "$CERT_FILE" "$KEY_FILE"
        log_success "TLS证书生成完成"
    fi
}

# 测试现有服务状态
test_existing_services() {
    echo -e "${YELLOW}正在测试现有服务状态...${NC}"
    echo ""
    
    local server_ip=$(get_server_ip)
    if [[ -z "$server_ip" ]]; then
        log_error "无法获取服务器IP"
        return 1
    fi
    
    log_info "服务器IP: $server_ip"
    echo ""
    
    # 检查Hysteria 2进程
    echo -e "${BLUE}=== Hysteria 2 服务状态 ===${NC}"
    local hysteria_processes=$(ps aux | grep hysteria | grep -v grep)
    if [[ -n "$hysteria_processes" ]]; then
        log_success "发现运行中的Hysteria 2服务:"
        echo "$hysteria_processes"
    else
        log_warn "没有运行的Hysteria 2服务"
    fi
    echo ""
    
    # 检查SSH隧道
    echo -e "${BLUE}=== SSH隧道状态 ===${NC}"
    local ssh_tunnel=$(ps aux | grep "ssh.*-D.*1080" | grep -v grep)
    if [[ -n "$ssh_tunnel" ]]; then
        log_success "发现运行中的SSH隧道:"
        echo "$ssh_tunnel"
        
        # 测试SSH隧道连接
        log_info "测试SSH隧道连接..."
        local ssh_result=$(timeout 10 curl -s --socks5-hostname 127.0.0.1:1080 --max-time 10 "http://httpbin.org/ip" 2>/dev/null)
        if [[ $? -eq 0 && -n "$ssh_result" ]]; then
            local proxy_ip=$(echo "$ssh_result" | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
            log_success "SSH隧道工作正常，代理IP: $proxy_ip"
        else
            log_warn "SSH隧道连接测试失败"
        fi
    else
        log_warn "没有运行的SSH隧道"
    fi
    echo ""
    
    # 检查端口占用
    echo -e "${BLUE}=== 端口占用情况 ===${NC}"
    local ports=$(netstat -tulpn 2>/dev/null | grep -E ":(9443|9444|1080|8443|8444) ")
    if [[ -n "$ports" ]]; then
        echo "$ports"
    else
        log_info "没有发现相关端口占用"
    fi
    echo ""
    
    # 检查systemd服务
    echo -e "${BLUE}=== Systemd 服务状态 ===${NC}"
    for service in hysteria-server hysteria2-socks5 hysteria2-ssh ssh-tunnel; do
        if systemctl list-unit-files | grep -q "$service.service"; then
            local status=$(systemctl is-active "$service" 2>/dev/null || echo "inactive")
            if [[ "$status" == "active" ]]; then
                log_success "$service: 运行中"
            else
                log_warn "$service: 未运行"
            fi
        fi
    done
    echo ""
    
    # 检查配置文件
    echo -e "${BLUE}=== 配置文件状态 ===${NC}"
    for config in "$SOCKS5_CONFIG" "$SSH_CONFIG" "/etc/hysteria/config.yaml"; do
        if [[ -f "$config" ]]; then
            log_success "配置文件存在: $config"
        fi
    done
    
    echo ""
    echo -n -e "${CYAN}按回车键继续...${NC}"
    read
}

# 解析SSH代理配置
parse_ssh_proxy() {
    local ssh_url="$1"
    if [[ "$ssh_url" =~ ^ssh://([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+):([0-9]+):([^:]+):(.+)$ ]]; then
        SSH_HOST="${BASH_REMATCH[1]}"
        SSH_PORT="${BASH_REMATCH[2]}"
        SSH_USER="${BASH_REMATCH[3]}"
        SSH_PASS="${BASH_REMATCH[4]}"
        return 0
    else
        log_error "SSH代理URL格式错误"
        return 1
    fi
}

# 解析SOCKS5代理配置
parse_socks5_proxy() {
    local socks5_url="$1"
    if [[ "$socks5_url" =~ ^socks5://([^:]+):([0-9]+):([^:]+):(.+)$ ]]; then
        SOCKS5_HOST="${BASH_REMATCH[1]}"
        SOCKS5_PORT="${BASH_REMATCH[2]}"
        SOCKS5_USER="${BASH_REMATCH[3]}"
        SOCKS5_PASS="${BASH_REMATCH[4]}"
        return 0
    else
        log_error "SOCKS5代理URL格式错误"
        return 1
    fi
}

# 测试SSH代理连接
test_ssh_proxy() {
    local ssh_host="$1"
    local ssh_port="$2"
    local ssh_user="$3"
    local ssh_pass="$4"

    log_info "测试SSH代理连接到 $ssh_user@$ssh_host:$ssh_port"

    # 测试端口连通性
    if ! timeout 10 nc -z "$ssh_host" "$ssh_port" 2>/dev/null; then
        log_error "无法连接到SSH服务器 $ssh_host:$ssh_port"
        return 1
    fi

    log_success "SSH端口连通性测试通过"

    # 测试SSH认证
    local test_result=$(timeout 15 sshpass -p "$ssh_pass" ssh -o StrictHostKeyChecking=no \
        -o ConnectTimeout=15 -o BatchMode=yes -o UserKnownHostsFile=/dev/null \
        -p "$ssh_port" "$ssh_user@$ssh_host" "echo 'SSH_TEST_OK'" 2>/dev/null)

    if [[ "$test_result" == "SSH_TEST_OK" ]]; then
        log_success "SSH认证测试通过"
        return 0
    else
        log_warn "SSH认证测试失败，但端口可达"
        return 1
    fi
}

# 测试SOCKS5代理连接
test_socks5_proxy() {
    local proxy_host="$1"
    local proxy_port="$2"
    local proxy_user="$3"
    local proxy_pass="$4"

    log_info "测试SOCKS5代理连接到 $proxy_user@$proxy_host:$proxy_port"

    # 测试端口连通性
    if ! timeout 10 nc -z "$proxy_host" "$proxy_port" 2>/dev/null; then
        log_error "无法连接到SOCKS5服务器 $proxy_host:$proxy_port"
        return 1
    fi

    log_success "SOCKS5端口连通性测试通过"

    # 测试SOCKS5代理
    local test_result=$(timeout 15 curl -s --socks5-hostname "$proxy_user:$proxy_pass@$proxy_host:$proxy_port" \
        --max-time 15 --connect-timeout 10 "http://httpbin.org/ip" 2>/dev/null)

    if [[ $? -eq 0 && -n "$test_result" ]]; then
        local proxy_ip=$(echo "$test_result" | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
        if [[ -n "$proxy_ip" ]]; then
            log_success "SOCKS5代理测试通过，代理IP: $proxy_ip"
            return 0
        fi
    fi

    log_warn "SOCKS5代理功能测试失败，但端口可达"
    return 1
}

# 选择可用端口
select_available_port() {
    local start_port=8000
    local end_port=9999

    for ((port=start_port; port<=end_port; port++)); do
        if check_port $port; then
            echo $port
            return 0
        fi
    done

    log_error "无法找到可用端口"
    return 1
}

# 创建SSH隧道服务
create_ssh_tunnel_service() {
    local ssh_host="$1"
    local ssh_port="$2"
    local ssh_user="$3"
    local ssh_pass="$4"
    local local_port="$5"

    log_info "创建SSH隧道服务..."

    cat > /etc/systemd/system/ssh-tunnel.service << EOF
[Unit]
Description=SSH SOCKS5 Tunnel for Hysteria2
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/bin/sshpass -p '$ssh_pass' /usr/bin/ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ServerAliveInterval=60 -o ServerAliveCountMax=3 -N -D 0.0.0.0:$local_port -p $ssh_port $ssh_user@$ssh_host
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable ssh-tunnel

    log_info "启动SSH隧道服务..."
    systemctl restart ssh-tunnel
    sleep 3

    if systemctl is-active --quiet ssh-tunnel; then
        log_success "SSH隧道服务启动成功"
        return 0
    else
        log_error "SSH隧道服务启动失败"
        journalctl -u ssh-tunnel --no-pager -n 10
        return 1
    fi
}

# 生成Hysteria 2 SOCKS5配置
generate_socks5_config() {
    local port="$1"
    local password="$2"
    local proxy_host="$3"
    local proxy_port="$4"
    local proxy_user="$5"
    local proxy_pass="$6"

    log_info "生成Hysteria 2 SOCKS5配置..."

    cat > "$SOCKS5_CONFIG" << EOF
listen: :$port

tls:
  cert: $CERT_FILE
  key: $KEY_FILE

auth:
  type: password
  password: $password

masquerade:
  type: proxy
  proxy:
    url: https://www.bing.com
    rewriteHost: true

quic:
  initStreamReceiveWindow: 8388608
  maxStreamReceiveWindow: 8388608
  initConnReceiveWindow: 20971520
  maxConnReceiveWindow: 20971520
  maxIdleTimeout: 30s
  maxIncomingStreams: 1024
  disablePathMTUDiscovery: false

bandwidth:
  up: 1 gbps
  down: 1 gbps

ignoreClientBandwidth: false
disableUDP: false
udpIdleTimeout: 60s

resolver:
  type: udp
  udp:
    addr: *******:53
    timeout: 4s

outbounds:
  - name: socks5proxy
    type: socks5
    socks5:
      addr: $proxy_host:$proxy_port
      username: $proxy_user
      password: $proxy_pass
  - name: direct
    type: direct

acl:
  inline:
    - "socks5proxy(all)"
EOF

    log_success "SOCKS5配置文件生成完成: $SOCKS5_CONFIG"
}

# 生成Hysteria 2 SSH配置
generate_ssh_config() {
    local port="$1"
    local password="$2"
    local local_socks_port="$3"

    log_info "生成Hysteria 2 SSH配置..."

    cat > "$SSH_CONFIG" << EOF
listen: :$port

tls:
  cert: $CERT_FILE
  key: $KEY_FILE

auth:
  type: password
  password: $password

masquerade:
  type: proxy
  proxy:
    url: https://www.bing.com
    rewriteHost: true

quic:
  initStreamReceiveWindow: 8388608
  maxStreamReceiveWindow: 8388608
  initConnReceiveWindow: 20971520
  maxConnReceiveWindow: 20971520
  maxIdleTimeout: 30s
  maxIncomingStreams: 1024
  disablePathMTUDiscovery: false

bandwidth:
  up: 1 gbps
  down: 1 gbps

ignoreClientBandwidth: false
disableUDP: false
udpIdleTimeout: 60s

resolver:
  type: udp
  udp:
    addr: *******:53
    timeout: 4s

outbounds:
  - name: sshproxy
    type: socks5
    socks5:
      addr: 127.0.0.1:$local_socks_port
  - name: direct
    type: direct

acl:
  inline:
    - "sshproxy(all)"
EOF

    log_success "SSH配置文件生成完成: $SSH_CONFIG"
}

# 创建Hysteria 2服务
create_hysteria_service() {
    local service_name="$1"
    local config_file="$2"
    local description="$3"

    log_info "创建Hysteria 2服务: $service_name"

    cat > "/etc/systemd/system/$service_name.service" << EOF
[Unit]
Description=$description
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/hysteria server --config $config_file
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable "$service_name"

    log_info "启动服务: $service_name"
    systemctl restart "$service_name"
    sleep 3

    if systemctl is-active --quiet "$service_name"; then
        log_success "服务 $service_name 启动成功"
        return 0
    else
        log_error "服务 $service_name 启动失败"
        journalctl -u "$service_name" --no-pager -n 10
        return 1
    fi
}

# 配置静态住宅IP代理服务
setup_static_ip_proxy() {
    echo -e "${YELLOW}配置静态住宅IP代理服务 (SSH隧道)${NC}"
    echo ""

    # 解析SSH代理配置
    if ! parse_ssh_proxy "$SSH_PROXY"; then
        log_error "SSH代理配置解析失败"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    log_info "SSH代理配置:"
    echo "  主机: $SSH_HOST"
    echo "  端口: $SSH_PORT"
    echo "  用户: $SSH_USER"
    echo ""

    # 测试SSH代理连接
    if ! test_ssh_proxy "$SSH_HOST" "$SSH_PORT" "$SSH_USER" "$SSH_PASS"; then
        log_warn "SSH代理测试失败，是否继续配置? [y/N]"
        read -r confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            echo -n -e "${CYAN}按回车键继续...${NC}"
            read
            return 0
        fi
    fi

    # 设置环境
    setup_environment

    # 选择端口
    local hy2_port=$(select_available_port)
    if [[ -z "$hy2_port" ]]; then
        log_error "无法找到可用端口"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    local ssh_tunnel_port=1080
    local password=$(generate_password)

    log_info "配置信息:"
    echo "  Hysteria 2端口: $hy2_port"
    echo "  SSH隧道端口: $ssh_tunnel_port"
    echo "  密码: $password"
    echo ""

    # 创建SSH隧道服务
    if ! create_ssh_tunnel_service "$SSH_HOST" "$SSH_PORT" "$SSH_USER" "$SSH_PASS" "$ssh_tunnel_port"; then
        log_error "SSH隧道服务创建失败"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    # 生成Hysteria 2配置
    generate_ssh_config "$hy2_port" "$password" "$ssh_tunnel_port"

    # 创建Hysteria 2服务
    if ! create_hysteria_service "hysteria2-ssh" "$SSH_CONFIG" "Hysteria2 SSH Proxy Server"; then
        log_error "Hysteria 2 SSH服务创建失败"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    # 获取服务器IP
    local server_ip=$(get_server_ip)

    echo ""
    log_success "=== 静态住宅IP代理服务配置完成 ==="
    echo -e "${GREEN}服务器IP:${NC} $server_ip"
    echo -e "${GREEN}端口:${NC} $hy2_port"
    echo -e "${GREEN}密码:${NC} $password"
    echo -e "${GREEN}代理类型:${NC} SSH静态IP ($SSH_HOST)"
    echo -e "${GREEN}客户端链接:${NC}"
    echo "hysteria2://$password@$server_ip:$hy2_port/?insecure=1#Hysteria2-SSH-Static"
    echo ""

    echo -n -e "${CYAN}按回车键继续...${NC}"
    read
}

# 配置动态IP代理服务
setup_dynamic_ip_proxy() {
    echo -e "${YELLOW}配置动态IP代理服务 (SOCKS5)${NC}"
    echo ""

    # 解析SOCKS5代理配置
    if ! parse_socks5_proxy "$SOCKS5_PROXY"; then
        log_error "SOCKS5代理配置解析失败"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    log_info "SOCKS5代理配置:"
    echo "  主机: $SOCKS5_HOST"
    echo "  端口: $SOCKS5_PORT"
    echo "  用户: $SOCKS5_USER"
    echo ""

    # 测试SOCKS5代理连接
    if ! test_socks5_proxy "$SOCKS5_HOST" "$SOCKS5_PORT" "$SOCKS5_USER" "$SOCKS5_PASS"; then
        log_warn "SOCKS5代理测试失败，是否继续配置? [y/N]"
        read -r confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            echo -n -e "${CYAN}按回车键继续...${NC}"
            read
            return 0
        fi
    fi

    # 设置环境
    setup_environment

    # 选择端口
    local hy2_port=$(select_available_port)
    if [[ -z "$hy2_port" ]]; then
        log_error "无法找到可用端口"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    local password=$(generate_password)

    log_info "配置信息:"
    echo "  Hysteria 2端口: $hy2_port"
    echo "  密码: $password"
    echo ""

    # 生成Hysteria 2配置
    generate_socks5_config "$hy2_port" "$password" "$SOCKS5_HOST" "$SOCKS5_PORT" "$SOCKS5_USER" "$SOCKS5_PASS"

    # 创建Hysteria 2服务
    if ! create_hysteria_service "hysteria2-socks5" "$SOCKS5_CONFIG" "Hysteria2 SOCKS5 Proxy Server"; then
        log_error "Hysteria 2 SOCKS5服务创建失败"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    # 获取服务器IP
    local server_ip=$(get_server_ip)

    echo ""
    log_success "=== 动态IP代理服务配置完成 ==="
    echo -e "${GREEN}服务器IP:${NC} $server_ip"
    echo -e "${GREEN}端口:${NC} $hy2_port"
    echo -e "${GREEN}密码:${NC} $password"
    echo -e "${GREEN}代理类型:${NC} SOCKS5动态IP"
    echo -e "${GREEN}客户端链接:${NC}"
    echo "hysteria2://$password@$server_ip:$hy2_port/?insecure=1#Hysteria2-SOCKS5-Dynamic"
    echo ""

    echo -n -e "${CYAN}按回车键继续...${NC}"
    read
}

# 显示现有协议服务配置信息
show_service_info() {
    echo -e "${YELLOW}显示现有协议服务配置信息${NC}"
    echo ""

    local server_ip=$(get_server_ip)
    if [[ -z "$server_ip" ]]; then
        log_error "无法获取服务器IP"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    echo -e "${BLUE}=== 服务器信息 ===${NC}"
    echo -e "${GREEN}服务器IP:${NC} $server_ip"
    echo ""

    # 检查Hysteria 2服务
    echo -e "${BLUE}=== Hysteria 2 服务状态 ===${NC}"
    local found_services=false

    for service in hysteria2-socks5 hysteria2-ssh hysteria-server; do
        if systemctl list-unit-files | grep -q "$service.service"; then
            local status=$(systemctl is-active "$service" 2>/dev/null || echo "inactive")
            if [[ "$status" == "active" ]]; then
                log_success "$service: 运行中"
                found_services=true

                # 获取配置信息
                local config_file=""
                case "$service" in
                    "hysteria2-socks5")
                        config_file="$SOCKS5_CONFIG"
                        ;;
                    "hysteria2-ssh")
                        config_file="$SSH_CONFIG"
                        ;;
                    "hysteria-server")
                        config_file="/etc/hysteria/config.yaml"
                        ;;
                esac

                if [[ -f "$config_file" ]]; then
                    local port=$(grep "listen:" "$config_file" | awk '{print $2}' | sed 's/://')
                    local password=$(grep "password:" "$config_file" | head -1 | awk '{print $2}')

                    echo "  配置文件: $config_file"
                    echo "  端口: $port"
                    echo "  密码: $password"

                    # 检测代理类型
                    if grep -q "type: socks5" "$config_file"; then
                        local socks5_addr=$(grep "addr:" "$config_file" | grep -v "*******" | awk '{print $2}')
                        echo "  代理类型: SOCKS5动态IP"
                        echo "  上游代理: $socks5_addr"
                        echo "  客户端链接: hysteria2://$password@$server_ip:$port/?insecure=1#Hysteria2-SOCKS5-Dynamic"
                    elif grep -q "127.0.0.1:1080" "$config_file"; then
                        echo "  代理类型: SSH静态IP"
                        echo "  本地隧道: 127.0.0.1:1080"
                        echo "  客户端链接: hysteria2://$password@$server_ip:$port/?insecure=1#Hysteria2-SSH-Static"
                    fi
                    echo ""
                fi
            else
                log_warn "$service: 未运行"
            fi
        fi
    done

    if ! $found_services; then
        log_info "没有发现运行中的Hysteria 2服务"
    fi

    # 检查SSH隧道
    echo -e "${BLUE}=== SSH隧道状态 ===${NC}"
    if systemctl list-unit-files | grep -q "ssh-tunnel.service"; then
        local status=$(systemctl is-active ssh-tunnel 2>/dev/null || echo "inactive")
        if [[ "$status" == "active" ]]; then
            log_success "SSH隧道服务: 运行中"

            # 测试隧道连接
            local ssh_result=$(timeout 5 curl -s --socks5-hostname 127.0.0.1:1080 --max-time 5 "http://httpbin.org/ip" 2>/dev/null)
            if [[ $? -eq 0 && -n "$ssh_result" ]]; then
                local proxy_ip=$(echo "$ssh_result" | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
                echo "  隧道状态: 正常"
                echo "  代理IP: $proxy_ip"
            else
                echo "  隧道状态: 异常"
            fi
        else
            log_warn "SSH隧道服务: 未运行"
        fi
    else
        log_info "没有配置SSH隧道服务"
    fi

    echo ""

    # 显示端口占用
    echo -e "${BLUE}=== 端口占用情况 ===${NC}"
    local ports=$(netstat -tulpn 2>/dev/null | grep -E ":(8[0-9]{3}|9[0-9]{3}|1080) " | grep -E "(hysteria|ssh)")
    if [[ -n "$ports" ]]; then
        echo "$ports"
    else
        log_info "没有发现相关端口占用"
    fi

    echo ""

    # 显示配置文件
    echo -e "${BLUE}=== 配置文件 ===${NC}"
    for config in "$SOCKS5_CONFIG" "$SSH_CONFIG" "/etc/hysteria/config.yaml" "$CERT_FILE" "$KEY_FILE"; do
        if [[ -f "$config" ]]; then
            log_success "存在: $config"
        fi
    done

    echo ""
    echo -n -e "${CYAN}按回车键继续...${NC}"
    read
}
