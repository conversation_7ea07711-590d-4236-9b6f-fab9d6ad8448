# Hysteria 2 代理服务使用指南

## 🎉 配置完成！

您的Hysteria 2代理服务已成功配置并运行。现在您可以使用静态IP和动态IP两种代理模式。

## 📋 服务概览

### 当前运行的服务
✅ **SSH隧道服务** - 端口1080 (本地SOCKS5代理)  
✅ **Hysteria 2 SOCKS5服务** - 端口9443 (动态IP代理)  
✅ **Hysteria 2 SSH服务** - 端口9444 (静态IP代理)  

### 服务器信息
- **服务器IP**: *************
- **密码**: test123456
- **协议**: Hysteria 2
- **加密**: TLS (自签名证书)

## 🔗 客户端连接配置

### 1. 动态IP SOCKS5代理
```
hysteria2://test123456@*************:9443/?insecure=1#Hysteria2-SOCKS5-Dynamic
```
- **特点**: 动态IP，每次连接可能获得不同的IP地址
- **适用场景**: 需要频繁更换IP的应用

### 2. 静态IP SSH代理
```
hysteria2://test123456@*************:9444/?insecure=1#Hysteria2-SSH-Static
```
- **特点**: 静态IP (*************)，IP地址固定不变
- **适用场景**: 需要固定IP的应用，如网站登录、API调用等

## 📱 客户端软件推荐

### Windows/Mac/Linux
- **Clash Meta** (推荐)
- **v2rayN** (Windows)
- **ClashX** (Mac)

### Android
- **Clash for Android**
- **v2rayNG**

### iOS
- **Shadowrocket**
- **Quantumult X**

## ⚙️ 客户端配置说明

### 重要参数
- `insecure=1`: 跳过TLS证书验证（必须设置）
- 协议: Hysteria 2
- 端口: 9443 (动态) / 9444 (静态)
- 密码: test123456

### Clash配置示例
```yaml
proxies:
  - name: "Hysteria2-SOCKS5-Dynamic"
    type: hysteria2
    server: *************
    port: 9443
    password: test123456
    skip-cert-verify: true
    
  - name: "Hysteria2-SSH-Static"
    type: hysteria2
    server: *************
    port: 9444
    password: test123456
    skip-cert-verify: true
```

## 🛠️ 服务管理

### 交互式管理脚本
```bash
./hysteria2_interactive_manager.sh
```

### 手动管理命令
```bash
# 查看服务状态
netstat -tulpn | grep -E ":(9443|9444|1080)"

# 查看进程
ps aux | grep hysteria
ps aux | grep "ssh.*-D.*1080"

# 重启SOCKS5服务
/usr/local/bin/hysteria server --config config-socks5.yaml

# 重启SSH服务
/usr/local/bin/hysteria server --config config-ssh-tunnel.yaml

# 重启SSH隧道
sshpass -p "yHB9v0LDXZwTruJ1nY" ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ServerAliveInterval=60 -o ServerAliveCountMax=3 -N -D 0.0.0.0:1080 -p 43001 ANo3v2iTBI1HfgLe7b@*************
```

## 🧪 连接测试

### 测试SSH隧道
```bash
curl -s --socks5-hostname 127.0.0.1:1080 "http://httpbin.org/ip"
# 应该返回: {"origin": "*************"}
```

### 测试客户端连接
1. 配置客户端使用上述连接信息
2. 连接后访问 http://httpbin.org/ip 查看代理IP
3. 动态代理应显示美国IP
4. 静态代理应显示 *************

## 🔧 故障排除

### 常见问题

**1. 连接失败**
- 检查防火墙是否开放端口9443和9444
- 确认客户端设置了 `insecure=1` 或 `skip-cert-verify: true`

**2. SSH隧道断开**
- 重新运行SSH隧道命令
- 检查SSH服务器状态

**3. 代理IP不正确**
- 动态代理: 检查SOCKS5上游服务器状态
- 静态代理: 检查SSH隧道是否正常

### 日志查看
```bash
# 查看Hysteria 2日志
journalctl -f -u hysteria-server

# 查看系统日志
tail -f /var/log/syslog | grep hysteria
```

## 🔒 安全建议

1. **定期更换密码**: 建议每月更换服务密码
2. **使用SSH密钥**: 将SSH密码认证改为密钥认证
3. **监控连接**: 定期检查异常连接
4. **备份配置**: 保存配置文件备份

## 📞 技术支持

如果遇到问题，请提供以下信息：
- 客户端软件和版本
- 错误信息截图
- 服务器日志
- 网络环境描述

---

## 🎯 快速开始

1. **复制连接链接**到客户端
2. **设置跳过证书验证**
3. **选择合适的代理模式**（静态/动态）
4. **开始使用**

**动态IP**: `hysteria2://test123456@*************:9443/?insecure=1#Hysteria2-SOCKS5-Dynamic`

**静态IP**: `hysteria2://test123456@*************:9444/?insecure=1#Hysteria2-SSH-Static`

---

✨ **配置完成，享受高速稳定的代理服务！** ✨
