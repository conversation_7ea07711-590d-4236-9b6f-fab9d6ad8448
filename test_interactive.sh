#!/bin/bash

# 测试交互式脚本功能

echo "=== Hysteria 2 代理服务测试 ==="
echo ""

# 显示当前运行的服务
echo "当前运行的服务:"
echo ""

# 检查Hysteria 2进程
hysteria_processes=$(ps aux | grep hysteria | grep -v grep)
if [[ -n "$hysteria_processes" ]]; then
    echo "✅ Hysteria 2服务:"
    echo "$hysteria_processes"
else
    echo "❌ 没有运行的Hysteria 2服务"
fi

echo ""

# 检查SSH隧道
ssh_tunnel=$(ps aux | grep "ssh.*-D.*1080" | grep -v grep)
if [[ -n "$ssh_tunnel" ]]; then
    echo "✅ SSH隧道服务:"
    echo "$ssh_tunnel"
else
    echo "❌ 没有运行的SSH隧道"
fi

echo ""

# 检查端口占用
echo "端口占用情况:"
netstat -tulpn | grep -E ":(9443|9444|1080) "

echo ""

# 测试代理连接
echo "=== 代理连接测试 ==="
echo ""

# 测试SSH隧道
echo "1. 测试SSH隧道 (127.0.0.1:1080):"
ssh_result=$(timeout 10 curl -s --socks5-hostname 127.0.0.1:1080 --max-time 10 "http://httpbin.org/ip" 2>/dev/null)
if [[ $? -eq 0 && -n "$ssh_result" ]]; then
    proxy_ip=$(echo "$ssh_result" | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
    echo "✅ SSH隧道工作正常，代理IP: $proxy_ip"
else
    echo "❌ SSH隧道连接失败"
fi

echo ""

# 显示客户端配置
echo "=== 客户端配置 ==="
echo ""

server_ip="*************"
password="test123456"

echo "服务器IP: $server_ip"
echo "密码: $password"
echo ""

echo "SOCKS5动态IP服务 (端口9443):"
echo "hysteria2://$password@$server_ip:9443/?insecure=1#Hysteria2-SOCKS5-Dynamic"
echo ""

echo "SSH静态IP服务 (端口9444):"
echo "hysteria2://$password@$server_ip:9444/?insecure=1#Hysteria2-SSH-Static"
echo ""

echo "=== 配置文件位置 ==="
echo "SOCKS5配置: $(pwd)/config-socks5.yaml"
echo "SSH配置: $(pwd)/config-ssh-tunnel.yaml"
echo "TLS证书: $(pwd)/cert.pem"
echo "私钥: $(pwd)/private.key"

echo ""
echo "=== 测试完成 ==="
