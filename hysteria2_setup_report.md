# Hysteria 2 代理服务配置报告

## 配置概述

已成功配置了两个Hysteria 2代理服务，支持静态IP和动态IP代理：

### 1. 动态IP SOCKS5代理服务
- **端口**: 9443
- **密码**: test123456
- **代理类型**: SOCKS5动态IP
- **上游代理**: gate-us.ipfoxy.io:58688
- **配置文件**: config-socks5.yaml

### 2. 静态IP SSH代理服务
- **端口**: 9444
- **密码**: test123456
- **代理类型**: SSH静态IP
- **上游代理**: *************:43001 (通过SSH隧道)
- **配置文件**: config-ssh-tunnel.yaml

## 服务器信息
- **服务器IP**: *************
- **TLS证书**: 自签名证书 (cert.pem)
- **私钥**: private.key

## 客户端连接配置

### SOCKS5动态IP服务
```
hysteria2://test123456@*************:9443/?insecure=1#Hysteria2-SOCKS5-Dynamic
```

### SSH静态IP服务
```
hysteria2://test123456@*************:9444/?insecure=1#Hysteria2-SSH-Static
```

## 代理测试结果

### SOCKS5代理测试
- ✅ 端口连通性: gate-us.ipfoxy.io:58688 可达
- ✅ Hysteria 2服务: 端口9443正常运行

### SSH代理测试
- ✅ SSH隧道: *************:43001 连接成功
- ✅ 本地SOCKS5隧道: 127.0.0.1:1080 工作正常
- ✅ 代理IP验证: ************* (静态IP)
- ✅ Hysteria 2服务: 端口9444正常运行

## 服务管理

### 当前运行的服务
1. Hysteria 2 SOCKS5服务 (端口9443)
2. Hysteria 2 SSH服务 (端口9444)
3. SSH隧道服务 (本地端口1080)

### 服务控制命令
```bash
# 查看Hysteria 2 SOCKS5服务状态
/usr/local/bin/hysteria server --config config-socks5.yaml

# 查看Hysteria 2 SSH服务状态
/usr/local/bin/hysteria server --config config-ssh-tunnel.yaml

# 查看SSH隧道状态
ps aux | grep ssh

# 测试SOCKS5代理
curl -s --socks5-hostname 127.0.0.1:1080 "http://httpbin.org/ip"
```

## 交互式管理脚本

已修改 `hysteria2_interactive_manager.sh` 脚本，支持：
- 选择静态IP (SSH) 或动态IP (SOCKS5) 代理
- 自动创建SSH隧道服务
- 代理连接测试
- 服务管理和监控

### 使用方法
```bash
chmod +x hysteria2_interactive_manager.sh
./hysteria2_interactive_manager.sh
```

## 配置文件说明

### SOCKS5配置 (config-socks5.yaml)
- 直接连接到上游SOCKS5代理服务器
- 支持用户名密码认证
- 适用于动态IP场景

### SSH隧道配置 (config-ssh-tunnel.yaml)
- 通过SSH隧道建立本地SOCKS5代理
- 连接到本地127.0.0.1:1080
- 适用于静态IP场景

## 安全注意事项

1. **证书安全**: 使用自签名证书，客户端需要设置 `insecure=1`
2. **密码安全**: 建议定期更换服务密码
3. **SSH密钥**: 建议使用SSH密钥认证替代密码认证
4. **防火墙**: 确保端口9443和9444在防火墙中开放

## 故障排除

### 常见问题
1. **端口被占用**: 修改配置文件中的端口号
2. **SSH连接失败**: 检查SSH服务器状态和网络连通性
3. **SOCKS5连接失败**: 验证上游代理服务器状态
4. **证书错误**: 确保客户端设置了 `insecure=1`

### 日志查看
```bash
# 查看Hysteria 2日志
journalctl -f -u hysteria-server

# 查看SSH隧道日志
journalctl -f -u ssh-tunnel
```

## 总结

✅ 成功配置了Hysteria 2 + 静态IP SSH代理服务
✅ 成功配置了Hysteria 2 + 动态IP SOCKS5代理服务
✅ 代理连接测试通过
✅ 交互式管理脚本已更新
✅ 服务正常运行，可供客户端连接使用
