listen: :9444

tls:
  cert: cert.pem
  key: private.key

auth:
  type: password
  password: test123456

masquerade:
  type: proxy
  proxy:
    url: https://www.bing.com
    rewriteHost: true

quic:
  initStreamReceiveWindow: 8388608
  maxStreamReceiveWindow: 8388608
  initConnReceiveWindow: 20971520
  maxConnReceiveWindow: 20971520
  maxIdleTimeout: 30s
  maxIncomingStreams: 1024
  disablePathMTUDiscovery: false

bandwidth:
  up: 1 gbps
  down: 1 gbps

ignoreClientBandwidth: false
disableUDP: false
udpIdleTimeout: 60s

resolver:
  type: udp
  udp:
    addr: *******:53
    timeout: 4s

outbounds:
  - name: sshproxy
    type: socks5
    socks5:
      addr: 127.0.0.1:1080
  - name: direct
    type: direct

acl:
  inline:
    - "sshproxy(all)"
