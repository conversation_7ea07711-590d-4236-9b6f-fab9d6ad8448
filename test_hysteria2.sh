#!/bin/bash

# 简化的Hysteria 2测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
HYSTERIA_CONFIG="/etc/hysteria/config.yaml"
HYSTERIA_CERT="/etc/hysteria/cert.pem"
HYSTERIA_KEY="/etc/hysteria/private.key"
SERVICE_NAME="hysteria-server"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 生成自签名证书
generate_certificates() {
    log_info "正在生成TLS证书..."
    
    mkdir -p /etc/hysteria
    
    # 生成RSA私钥
    openssl genrsa -out "$HYSTERIA_KEY" 2048 2>/dev/null
    
    # 生成自签名证书
    openssl req -new -x509 -key "$HYSTERIA_KEY" -out "$HYSTERIA_CERT" -days 365 -subj "/CN=bing.com" 2>/dev/null
    
    # 设置权限
    chmod 644 "$HYSTERIA_CERT"
    chmod 644 "$HYSTERIA_KEY"
    
    log_info "TLS证书生成完成"
}

# 生成Hysteria 2配置文件 - SOCKS5模式
generate_hysteria2_socks5_config() {
    local port="8443"
    local password="test123456"
    local proxy_host="gate-us.ipfoxy.io"
    local proxy_port="58688"
    local proxy_user="customer-Q0SXajDpWx-cc-US-st-NewYork-city-NewYork-sessid-1754480265_10000"
    local proxy_pass="vUguohGcRZK1S0s"

    log_info "正在生成Hysteria 2 SOCKS5配置文件..."

    cat > "$HYSTERIA_CONFIG" << EOF
listen: :$port

tls:
  cert: $HYSTERIA_CERT
  key: $HYSTERIA_KEY

auth:
  type: password
  password: $password

masquerade:
  type: proxy
  proxy:
    url: https://www.bing.com
    rewriteHost: true

quic:
  initStreamReceiveWindow: 8388608
  maxStreamReceiveWindow: 8388608
  initConnReceiveWindow: 20971520
  maxConnReceiveWindow: 20971520
  maxIdleTimeout: 30s
  maxIncomingStreams: 1024
  disablePathMTUDiscovery: false

bandwidth:
  up: 1 gbps
  down: 1 gbps

ignoreClientBandwidth: false
disableUDP: false
udpIdleTimeout: 60s

resolver:
  type: udp
  udp:
    addr: *******:53
    timeout: 4s

outbounds:
  - name: socks5proxy
    type: socks5
    socks5:
      addr: $proxy_host:$proxy_port
      username: $proxy_user
      password: $proxy_pass
  - name: direct
    type: direct

acl:
  inline:
    - "socks5proxy(all)"
EOF

    log_info "Hysteria 2 SOCKS5配置文件生成完成"
    log_info "服务器端口: $port"
    log_info "服务器密码: $password"
}

# 创建systemd服务
create_systemd_service() {
    log_info "正在创建systemd服务..."

    cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=Hysteria Server Service (config.yaml)
After=network.target nss-lookup.target

[Service]
Type=simple
StandardError=journal
User=root
AmbientCapabilities=CAP_NET_BIND_SERVICE
ExecStart=/usr/local/bin/hysteria server --config $HYSTERIA_CONFIG
ExecReload=/bin/kill -HUP \$MAINPID
Restart=on-failure
RestartSec=10
RestartPreventExitStatus=23

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    log_info "systemd服务创建完成"
}

# 启动服务
start_service() {
    log_info "正在启动Hysteria 2服务..."

    systemctl stop $SERVICE_NAME 2>/dev/null || true
    sleep 2
    systemctl start $SERVICE_NAME

    sleep 3

    if systemctl is-active --quiet $SERVICE_NAME; then
        log_info "Hysteria 2服务启动成功"
        return 0
    else
        log_error "Hysteria 2服务启动失败"
        journalctl -u $SERVICE_NAME --no-pager -n 10
        return 1
    fi
}

# 主函数
main() {
    check_root
    
    log_info "开始配置Hysteria 2 + SOCKS5代理服务"
    
    # 生成证书
    generate_certificates
    
    # 生成配置文件
    generate_hysteria2_socks5_config
    
    # 创建服务
    create_systemd_service
    
    # 启动服务
    if start_service; then
        log_info "=== 配置完成！==="
        log_info "服务器端口: 8443"
        log_info "服务器密码: test123456"
        log_info "代理类型: SOCKS5动态"
        
        # 获取服务器IP
        local server_ip=$(curl -s --max-time 10 ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP")
        log_info "导入链接: hysteria2://test123456@$server_ip:8443/?insecure=1#Hysteria2-SOCKS5-Test"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 运行主函数
main "$@"
