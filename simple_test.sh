#!/bin/bash

echo "=== Hysteria 2 代理服务测试 ==="
echo ""

# 检查Hysteria 2是否安装
if command -v /usr/local/bin/hysteria >/dev/null 2>&1; then
    echo "✓ Hysteria 2 已安装"
    /usr/local/bin/hysteria version
else
    echo "✗ Hysteria 2 未安装"
    exit 1
fi

echo ""

# 测试代理连接
echo "=== 测试代理连接 ==="

# 测试SOCKS5代理
echo "1. 测试SOCKS5代理连接..."
proxy_host="gate-us.ipfoxy.io"
proxy_port="58688"
proxy_user="customer-Q0SXajDpWx-cc-US-st-NewYork-city-NewYork-sessid-1754480265_10000"
proxy_pass="vUguohGcRZK1S0s"

# 测试端口连通性
if timeout 10 nc -z "$proxy_host" "$proxy_port" 2>/dev/null; then
    echo "✓ SOCKS5端口 $proxy_host:$proxy_port 可达"
else
    echo "✗ 无法连接到SOCKS5服务器 $proxy_host:$proxy_port"
fi

echo ""

# 测试SSH代理
echo "2. 测试SSH代理连接..."
ssh_host="*************"
ssh_port="43001"
ssh_user="ANo3v2iTBI1HfgLe7b"
ssh_pass="yHB9v0LDXZwTruJ1nY"

# 测试端口连通性
if timeout 10 nc -z "$ssh_host" "$ssh_port" 2>/dev/null; then
    echo "✓ SSH端口 $ssh_host:$ssh_port 可达"
else
    echo "✗ 无法连接到SSH服务器 $ssh_host:$ssh_port"
fi

echo ""

# 生成示例配置
echo "=== 生成示例配置 ==="

# 创建目录
mkdir -p /etc/hysteria

# 生成证书
echo "生成TLS证书..."
openssl genrsa -out /etc/hysteria/private.key 2048 2>/dev/null
openssl req -new -x509 -key /etc/hysteria/private.key -out /etc/hysteria/cert.pem -days 365 -subj "/CN=bing.com" 2>/dev/null
chmod 644 /etc/hysteria/cert.pem /etc/hysteria/private.key

echo "✓ TLS证书生成完成"

# 生成SOCKS5配置
cat > /etc/hysteria/config-socks5.yaml << EOF
listen: :8443

tls:
  cert: /etc/hysteria/cert.pem
  key: /etc/hysteria/private.key

auth:
  type: password
  password: test123456

masquerade:
  type: proxy
  proxy:
    url: https://www.bing.com
    rewriteHost: true

outbounds:
  - name: socks5proxy
    type: socks5
    socks5:
      addr: $proxy_host:$proxy_port
      username: $proxy_user
      password: $proxy_pass
  - name: direct
    type: direct

acl:
  inline:
    - "socks5proxy(all)"
EOF

echo "✓ SOCKS5配置文件生成: /etc/hysteria/config-socks5.yaml"

# 生成SSH配置
cat > /etc/hysteria/config-ssh.yaml << EOF
listen: :8444

tls:
  cert: /etc/hysteria/cert.pem
  key: /etc/hysteria/private.key

auth:
  type: password
  password: test123456

masquerade:
  type: proxy
  proxy:
    url: https://www.bing.com
    rewriteHost: true

outbounds:
  - name: sshproxy
    type: ssh
    ssh:
      addr: $ssh_host:$ssh_port
      user: $ssh_user
      password: $ssh_pass
      hostKey: []
      clientVersion: SSH-2.0-OpenSSH_8.9
  - name: direct
    type: direct

acl:
  inline:
    - "sshproxy(all)"
EOF

echo "✓ SSH配置文件生成: /etc/hysteria/config-ssh.yaml"

echo ""
echo "=== 配置完成 ==="
echo "SOCKS5代理服务端口: 8443"
echo "SSH代理服务端口: 8444"
echo "密码: test123456"
echo ""
echo "启动命令:"
echo "SOCKS5: /usr/local/bin/hysteria server --config /etc/hysteria/config-socks5.yaml"
echo "SSH: /usr/local/bin/hysteria server --config /etc/hysteria/config-ssh.yaml"
