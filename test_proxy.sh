#!/bin/bash

# 测试代理连接脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试SOCKS5代理
test_socks5() {
    local proxy_host="gate-us.ipfoxy.io"
    local proxy_port="58688"
    local proxy_user="customer-Q0SXajDpWx-cc-US-st-NewYork-city-NewYork-sessid-1754480265_10000"
    local proxy_pass="vUguohGcRZK1S0s"
    
    log_info "测试SOCKS5代理连接..."
    log_info "代理服务器: $proxy_host:$proxy_port"
    log_info "用户名: $proxy_user"
    
    # 测试连接并获取IP
    local result=$(timeout 15 curl -s --socks5-hostname "$proxy_user:$proxy_pass@$proxy_host:$proxy_port" \
        --max-time 15 \
        "http://httpbin.org/ip" 2>/dev/null)
    
    if [[ $? -eq 0 && -n "$result" ]]; then
        local proxy_ip=$(echo "$result" | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
        if [[ -n "$proxy_ip" ]]; then
            log_info "SOCKS5代理测试成功！"
            log_info "代理IP地址: $proxy_ip"
            
            # 获取IP地理位置信息
            local geo_info=$(timeout 10 curl -s --socks5-hostname "$proxy_user:$proxy_pass@$proxy_host:$proxy_port" \
                --max-time 10 \
                "http://ip-api.com/json/$proxy_ip" 2>/dev/null)
            
            if [[ $? -eq 0 && -n "$geo_info" ]]; then
                local country=$(echo "$geo_info" | grep -o '"country":"[^"]*"' | cut -d'"' -f4)
                local region=$(echo "$geo_info" | grep -o '"regionName":"[^"]*"' | cut -d'"' -f4)
                local city=$(echo "$geo_info" | grep -o '"city":"[^"]*"' | cut -d'"' -f4)
                local isp=$(echo "$geo_info" | grep -o '"isp":"[^"]*"' | cut -d'"' -f4)
                
                log_info "地理位置: $country, $region, $city"
                log_info "ISP: $isp"
            fi
            
            return 0
        fi
    fi
    
    log_error "SOCKS5代理测试失败"
    return 1
}

# 测试本地IP
test_local_ip() {
    log_info "测试本地IP..."
    
    local local_ip=$(timeout 10 curl -s --max-time 10 "http://httpbin.org/ip" 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
    
    if [[ -n "$local_ip" ]]; then
        log_info "本地IP地址: $local_ip"
        return 0
    else
        log_error "无法获取本地IP"
        return 1
    fi
}

# 主函数
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}        代理连接测试工具${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    # 测试本地IP
    test_local_ip
    echo ""
    
    # 测试SOCKS5代理
    test_socks5
    echo ""
    
    echo -e "${BLUE}测试完成！${NC}"
}

# 运行主函数
main "$@"
