#!/bin/bash

# 设置Hysteria 2服务自启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行"
    exit 1
fi

# 获取当前目录
CURRENT_DIR=$(pwd)

log_info "正在设置Hysteria 2服务..."

# 创建SSH隧道服务
log_info "创建SSH隧道服务..."
cat > /etc/systemd/system/ssh-tunnel.service << EOF
[Unit]
Description=SSH SOCKS5 Tunnel for Hysteria2
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$CURRENT_DIR
ExecStart=/usr/bin/sshpass -p 'yHB9v0LDXZwTruJ1nY' /usr/bin/ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ServerAliveInterval=60 -o ServerAliveCountMax=3 -N -D 0.0.0.0:1080 -p 43001 ANo3v2iTBI1HfgLe7b@*************
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 创建Hysteria 2 SOCKS5服务
log_info "创建Hysteria 2 SOCKS5服务..."
cat > /etc/systemd/system/hysteria2-socks5.service << EOF
[Unit]
Description=Hysteria2 SOCKS5 Proxy Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$CURRENT_DIR
ExecStart=/usr/local/bin/hysteria server --config $CURRENT_DIR/config-socks5.yaml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 创建Hysteria 2 SSH服务
log_info "创建Hysteria 2 SSH服务..."
cat > /etc/systemd/system/hysteria2-ssh.service << EOF
[Unit]
Description=Hysteria2 SSH Proxy Server
After=network.target ssh-tunnel.service
Requires=ssh-tunnel.service

[Service]
Type=simple
User=root
WorkingDirectory=$CURRENT_DIR
ExecStart=/usr/local/bin/hysteria server --config $CURRENT_DIR/config-ssh-tunnel.yaml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
log_info "重新加载systemd配置..."
systemctl daemon-reload

# 启用服务
log_info "启用服务自启动..."
systemctl enable ssh-tunnel
systemctl enable hysteria2-socks5
systemctl enable hysteria2-ssh

# 启动服务
log_info "启动服务..."
systemctl start ssh-tunnel
sleep 3
systemctl start hysteria2-socks5
systemctl start hysteria2-ssh

# 检查服务状态
log_info "检查服务状态..."
echo ""

if systemctl is-active --quiet ssh-tunnel; then
    log_info "✅ SSH隧道服务运行正常"
else
    log_error "❌ SSH隧道服务启动失败"
fi

if systemctl is-active --quiet hysteria2-socks5; then
    log_info "✅ Hysteria 2 SOCKS5服务运行正常"
else
    log_error "❌ Hysteria 2 SOCKS5服务启动失败"
fi

if systemctl is-active --quiet hysteria2-ssh; then
    log_info "✅ Hysteria 2 SSH服务运行正常"
else
    log_error "❌ Hysteria 2 SSH服务启动失败"
fi

echo ""
log_info "服务设置完成！"
log_info "所有服务已设置为开机自启动"

echo ""
log_info "服务管理命令:"
echo "systemctl status ssh-tunnel"
echo "systemctl status hysteria2-socks5"
echo "systemctl status hysteria2-ssh"
echo ""
echo "systemctl restart ssh-tunnel"
echo "systemctl restart hysteria2-socks5"
echo "systemctl restart hysteria2-ssh"
