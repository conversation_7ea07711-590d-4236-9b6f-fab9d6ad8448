#!/bin/bash

# 测试代理连接脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试SSH代理连接
test_ssh_proxy() {
    local ssh_host="*************"
    local ssh_port="43001"
    local ssh_user="ANo3v2iTBI1HfgLe7b"
    local ssh_pass="yHB9v0LDXZwTruJ1nY"

    log_info "正在测试SSH代理连接..."
    log_info "测试SSH连接到 $ssh_user@$ssh_host:$ssh_port"

    # 安装netcat如果没有
    if ! command -v nc >/dev/null 2>&1; then
        log_info "安装netcat..."
        apt update -qq && apt install -y netcat >/dev/null 2>&1
    fi

    # 测试端口连通性
    if timeout 10 nc -z "$ssh_host" "$ssh_port" 2>/dev/null; then
        log_info "SSH端口 $ssh_host:$ssh_port 可达"
        
        # 安装sshpass如果没有
        if ! command -v sshpass >/dev/null 2>&1; then
            log_info "安装sshpass..."
            apt update -qq && apt install -y sshpass >/dev/null 2>&1
        fi

        # 测试SSH连接
        if command -v sshpass >/dev/null 2>&1; then
            local test_result=$(timeout 15 sshpass -p "$ssh_pass" ssh -o StrictHostKeyChecking=no \
                -o ConnectTimeout=15 -o BatchMode=yes -o UserKnownHostsFile=/dev/null \
                -p "$ssh_port" "$ssh_user@$ssh_host" "echo 'SSH_TEST_OK'" 2>/dev/null)

            if [[ "$test_result" == "SSH_TEST_OK" ]]; then
                log_info "SSH代理测试成功！"
                return 0
            else
                log_warn "SSH认证失败"
            fi
        fi
    else
        log_error "无法连接到SSH服务器 $ssh_host:$ssh_port"
    fi

    return 1
}

# 测试SOCKS5代理连接
test_socks5_proxy() {
    local proxy_host="gate-us.ipfoxy.io"
    local proxy_port="58688"
    local proxy_user="customer-Q0SXajDpWx-cc-US-st-NewYork-city-NewYork-sessid-1754480265_10000"
    local proxy_pass="vUguohGcRZK1S0s"

    log_info "正在测试SOCKS5代理连接..."
    log_info "测试SOCKS5连接到 $proxy_user@$proxy_host:$proxy_port"

    # 安装netcat如果没有
    if ! command -v nc >/dev/null 2>&1; then
        log_info "安装netcat..."
        apt update -qq && apt install -y netcat >/dev/null 2>&1
    fi

    # 测试端口连通性
    if timeout 10 nc -z "$proxy_host" "$proxy_port" 2>/dev/null; then
        log_info "SOCKS5端口 $proxy_host:$proxy_port 可达"

        # 使用curl测试SOCKS5代理
        local test_result=$(timeout 15 curl -s --socks5-hostname "$proxy_user:$proxy_pass@$proxy_host:$proxy_port" \
            --max-time 15 --connect-timeout 10 \
            "http://httpbin.org/ip" 2>/dev/null)

        if [[ $? -eq 0 && -n "$test_result" ]]; then
            local proxy_ip=$(echo "$test_result" | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
            if [[ -n "$proxy_ip" ]]; then
                log_info "SOCKS5代理测试成功！代理IP: $proxy_ip"
                return 0
            else
                log_warn "SOCKS5代理响应格式异常: $test_result"
            fi
        else
            log_warn "SOCKS5代理连接失败"
        fi
    else
        log_error "无法连接到SOCKS5服务器 $proxy_host:$proxy_port"
    fi

    return 1
}

# 主函数
main() {
    echo -e "${BLUE}=== 代理连接测试 ===${NC}"
    echo ""

    echo -e "${YELLOW}1. 测试SSH代理${NC}"
    if test_ssh_proxy; then
        echo -e "${GREEN}SSH代理测试通过${NC}"
    else
        echo -e "${RED}SSH代理测试失败${NC}"
    fi

    echo ""
    echo -e "${YELLOW}2. 测试SOCKS5代理${NC}"
    if test_socks5_proxy; then
        echo -e "${GREEN}SOCKS5代理测试通过${NC}"
    else
        echo -e "${RED}SOCKS5代理测试失败${NC}"
    fi

    echo ""
    echo -e "${BLUE}测试完成${NC}"
}

# 运行主函数
main "$@"
