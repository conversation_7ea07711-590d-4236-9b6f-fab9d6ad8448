#!/bin/bash

# SOCKS5代理转发配置脚本
# 仅对Reality (VLESS)协议传入的流量进行SOCKS5代理转发
# 作者: AI Assistant
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置文件路径
SINGBOX_CONFIG="/usr/local/etc/sing-box/config.json"
BACKUP_DIR="/usr/local/etc/sing-box/backups"
SCRIPT_LOG="/var/log/socks5_proxy_setup.log"

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$SCRIPT_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$SCRIPT_LOG"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$SCRIPT_LOG"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$SCRIPT_LOG"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查sing-box是否存在
check_singbox() {
    if ! command -v sing-box &> /dev/null; then
        error "sing-box未安装或不在PATH中"
        exit 1
    fi
    
    if [[ ! -f "$SINGBOX_CONFIG" ]]; then
        error "sing-box配置文件不存在: $SINGBOX_CONFIG"
        exit 1
    fi
    
    if ! systemctl is-active --quiet sing-box; then
        warning "sing-box服务未运行"
    fi
}

# 创建备份目录
create_backup_dir() {
    if [[ ! -d "$BACKUP_DIR" ]]; then
        mkdir -p "$BACKUP_DIR"
        log "创建备份目录: $BACKUP_DIR"
    fi
}

# 备份当前配置
backup_config() {
    local backup_file="$BACKUP_DIR/config.json.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$SINGBOX_CONFIG" "$backup_file"
    log "配置已备份到: $backup_file"
    echo "$backup_file" > "$BACKUP_DIR/.last_backup"
}

# 收集SOCKS5代理信息
collect_proxy_info() {
    echo -e "\n${BLUE}=== SOCKS5代理配置 ===${NC}"
    echo "请输入您购买的北美SOCKS5住宅代理服务信息："
    echo
    
    # 代理服务器地址
    while true; do
        read -p "代理服务器地址 (IP或域名): " PROXY_HOST
        if [[ -n "$PROXY_HOST" ]]; then
            break
        else
            error "代理服务器地址不能为空"
        fi
    done
    
    # 代理端口
    while true; do
        read -p "代理端口: " PROXY_PORT
        if [[ "$PROXY_PORT" =~ ^[0-9]+$ ]] && [[ "$PROXY_PORT" -ge 1 ]] && [[ "$PROXY_PORT" -le 65535 ]]; then
            break
        else
            error "请输入有效的端口号 (1-65535)"
        fi
    done
    
    # 用户名
    read -p "用户名: " PROXY_USER
    
    # 密码
    echo -n "密码: "
    read -s PROXY_PASS
    echo
    
    # 确认信息
    echo -e "\n${YELLOW}请确认代理信息:${NC}"
    echo "服务器: $PROXY_HOST"
    echo "端口: $PROXY_PORT"
    echo "用户名: $PROXY_USER"
    echo "密码: $(echo "$PROXY_PASS" | sed 's/./*/g')"
    echo
    
    while true; do
        read -p "信息是否正确? (y/n): " confirm
        case $confirm in
            [Yy]* ) break;;
            [Nn]* ) 
                echo "请重新输入信息"
                collect_proxy_info
                return
                ;;
            * ) echo "请输入 y 或 n";;
        esac
    done
}

# 验证JSON格式
validate_json() {
    local json_file="$1"
    if ! python3 -m json.tool "$json_file" > /dev/null 2>&1; then
        if ! jq . "$json_file" > /dev/null 2>&1; then
            return 1
        fi
    fi
    return 0
}

# 修改sing-box配置
modify_singbox_config() {
    log "开始修改sing-box配置..."
    
    # 创建临时配置文件
    local temp_config="/tmp/singbox_config_temp.json"
    cp "$SINGBOX_CONFIG" "$temp_config"
    
    # 使用Python脚本修改JSON配置
    python3 << EOF
import json
import sys

try:
    # 读取配置文件
    with open('$temp_config', 'r') as f:
        config = json.load(f)
    
    # 添加SOCKS5出站配置
    socks5_outbound = {
        "type": "socks",
        "tag": "socks5-proxy",
        "server": "$PROXY_HOST",
        "server_port": $PROXY_PORT,
        "username": "$PROXY_USER",
        "password": "$PROXY_PASS",
        "version": "5"
    }
    
    # 确保outbounds数组存在
    if 'outbounds' not in config:
        config['outbounds'] = []
    
    # 检查是否已存在socks5-proxy配置
    existing_socks5 = False
    for i, outbound in enumerate(config['outbounds']):
        if outbound.get('tag') == 'socks5-proxy':
            config['outbounds'][i] = socks5_outbound
            existing_socks5 = True
            break
    
    if not existing_socks5:
        config['outbounds'].insert(0, socks5_outbound)
    
    # 修改路由规则
    if 'route' not in config:
        config['route'] = {}
    if 'rules' not in config['route']:
        config['route']['rules'] = []
    
    # 添加VLESS流量转发到SOCKS5的规则
    vless_rule = {
        "inbound": ["vless-in"],
        "outbound": "socks5-proxy"
    }
    
    # 检查是否已存在相同规则
    rule_exists = False
    for i, rule in enumerate(config['route']['rules']):
        if rule.get('inbound') == ["vless-in"]:
            config['route']['rules'][i] = vless_rule
            rule_exists = True
            break
    
    if not rule_exists:
        config['route']['rules'].insert(0, vless_rule)
    
    # 写入配置文件
    with open('$temp_config', 'w') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("配置修改成功")
    
except Exception as e:
    print(f"配置修改失败: {e}")
    sys.exit(1)
EOF

    if [[ $? -eq 0 ]]; then
        # 验证修改后的配置
        if validate_json "$temp_config"; then
            # 应用新配置
            cp "$temp_config" "$SINGBOX_CONFIG"
            rm "$temp_config"
            log "sing-box配置修改成功"
        else
            error "修改后的配置JSON格式无效"
            rm "$temp_config"
            exit 1
        fi
    else
        error "配置修改失败"
        rm -f "$temp_config"
        exit 1
    fi
}

# 重启sing-box服务
restart_singbox() {
    log "重启sing-box服务..."

    # 验证配置文件
    if ! sing-box check -c "$SINGBOX_CONFIG"; then
        error "sing-box配置文件验证失败"
        return 1
    fi

    # 重启服务
    if systemctl restart sing-box; then
        sleep 3
        if systemctl is-active --quiet sing-box; then
            log "sing-box服务重启成功"
            return 0
        else
            error "sing-box服务启动失败"
            return 1
        fi
    else
        error "sing-box服务重启失败"
        return 1
    fi
}

# 测试代理连接
test_proxy_connection() {
    log "测试SOCKS5代理连接..."

    # 使用curl测试代理连接
    if command -v curl &> /dev/null; then
        local test_url="http://httpbin.org/ip"
        local timeout=10

        info "测试代理连接到: $test_url"

        # 直连测试
        local direct_ip
        direct_ip=$(curl -s --max-time $timeout "$test_url" | grep -o '"origin": "[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "获取失败")
        info "直连IP: $direct_ip"

        # 代理测试
        local proxy_ip
        proxy_ip=$(curl -s --max-time $timeout --socks5-hostname "$PROXY_HOST:$PROXY_PORT" \
                   --proxy-user "$PROXY_USER:$PROXY_PASS" "$test_url" | \
                   grep -o '"origin": "[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "连接失败")

        if [[ "$proxy_ip" != "连接失败" ]] && [[ "$proxy_ip" != "$direct_ip" ]]; then
            log "SOCKS5代理连接测试成功，代理IP: $proxy_ip"
            return 0
        else
            warning "SOCKS5代理连接测试失败或IP未改变"
            return 1
        fi
    else
        warning "curl未安装，跳过代理连接测试"
        return 0
    fi
}

# 回滚配置
rollback_config() {
    local backup_file
    if [[ -f "$BACKUP_DIR/.last_backup" ]]; then
        backup_file=$(cat "$BACKUP_DIR/.last_backup")
        if [[ -f "$backup_file" ]]; then
            log "回滚到备份配置: $backup_file"
            cp "$backup_file" "$SINGBOX_CONFIG"

            if restart_singbox; then
                log "配置回滚成功"
                return 0
            else
                error "配置回滚后服务启动失败"
                return 1
            fi
        else
            error "备份文件不存在: $backup_file"
            return 1
        fi
    else
        error "未找到备份文件记录"
        return 1
    fi
}

# 显示当前配置状态
show_status() {
    echo -e "\n${BLUE}=== 当前配置状态 ===${NC}"

    # sing-box服务状态
    if systemctl is-active --quiet sing-box; then
        echo -e "sing-box服务: ${GREEN}运行中${NC}"
    else
        echo -e "sing-box服务: ${RED}未运行${NC}"
    fi

    # 检查配置中是否有SOCKS5代理
    if grep -q "socks5-proxy" "$SINGBOX_CONFIG" 2>/dev/null; then
        echo -e "SOCKS5代理: ${GREEN}已配置${NC}"

        # 提取代理信息
        local proxy_info
        proxy_info=$(python3 << 'EOF'
import json
try:
    with open('/usr/local/etc/sing-box/config.json', 'r') as f:
        config = json.load(f)

    for outbound in config.get('outbounds', []):
        if outbound.get('tag') == 'socks5-proxy':
            print(f"  服务器: {outbound.get('server', 'N/A')}")
            print(f"  端口: {outbound.get('server_port', 'N/A')}")
            print(f"  用户名: {outbound.get('username', 'N/A')}")
            break
except:
    print("  无法读取代理配置")
EOF
)
        echo "$proxy_info"
    else
        echo -e "SOCKS5代理: ${YELLOW}未配置${NC}"
    fi

    # 监听端口
    local listening_ports
    listening_ports=$(netstat -tlnp 2>/dev/null | grep sing-box | awk '{print $4}' | sed 's/.*://' | sort -n | tr '\n' ' ')
    echo "监听端口: ${listening_ports:-无}"

    echo
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}SOCKS5代理转发配置脚本${NC}"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  setup     - 交互式配置SOCKS5代理转发"
    echo "  status    - 显示当前配置状态"
    echo "  test      - 测试代理连接"
    echo "  rollback  - 回滚到上一个配置"
    echo "  help      - 显示此帮助信息"
    echo
    echo "功能说明:"
    echo "  此脚本仅对Reality (VLESS)协议(443端口)传入的流量进行SOCKS5代理转发"
    echo "  其他协议的流量将保持直连，不受影响"
    echo
}

# 主函数
main() {
    # 创建日志文件
    touch "$SCRIPT_LOG"

    case "${1:-setup}" in
        "setup")
            log "开始SOCKS5代理配置..."
            check_root
            check_singbox
            create_backup_dir
            backup_config
            collect_proxy_info
            modify_singbox_config

            if restart_singbox; then
                log "配置完成！"
                show_status

                echo -e "\n${GREEN}配置成功完成！${NC}"
                echo "现在通过Reality (VLESS)协议连接的流量将通过SOCKS5代理转发"
                echo "其他协议的流量保持直连"
                echo
                echo "建议运行测试: $0 test"
            else
                error "服务重启失败，正在回滚配置..."
                rollback_config
                exit 1
            fi
            ;;
        "status")
            show_status
            ;;
        "test")
            if [[ -z "$PROXY_HOST" ]]; then
                # 从配置文件读取代理信息进行测试
                eval $(python3 << 'EOF'
import json
try:
    with open('/usr/local/etc/sing-box/config.json', 'r') as f:
        config = json.load(f)

    for outbound in config.get('outbounds', []):
        if outbound.get('tag') == 'socks5-proxy':
            print(f"PROXY_HOST='{outbound.get('server', '')}'")
            print(f"PROXY_PORT='{outbound.get('server_port', '')}'")
            print(f"PROXY_USER='{outbound.get('username', '')}'")
            print(f"PROXY_PASS='{outbound.get('password', '')}'")
            break
except:
    pass
EOF
)
            fi
            test_proxy_connection
            ;;
        "rollback")
            check_root
            rollback_config
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
