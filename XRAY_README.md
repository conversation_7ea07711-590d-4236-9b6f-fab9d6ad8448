# Xray-core VLESS + Reality + SOCKS5 管理脚本

## 功能说明

这个脚本专门用于管理独立的Xray-core服务，实现VLESS + Reality协议并绑定SOCKS5代理转发。与现有的sing-box服务完全独立运行，互不影响。

## 核心特性

### ✅ **完全独立运行**
- 与sing-box服务完全隔离
- 使用不同的端口和配置文件
- 独立的systemd服务管理
- 互不影响的服务重启

### 🔍 **智能端口检测**
- 自动检测sing-box占用的端口
- 扫描系统已占用端口
- 推荐可用端口范围
- 交互式端口选择

### 🛡️ **VLESS + Reality协议**
- 最新的VLESS协议
- Reality TLS伪装技术
- 完美伪装HTTPS流量
- 极低的流量特征

### 🌐 **SOCKS5代理绑定**
- 自动配置住宅IP代理
- 支持用户名密码认证
- 所有流量通过SOCKS5转发
- 实现真正的住宅IP出口
- **与Xray-core服务完全同步启停**
- **自动连接状态检测和验证**

## 系统要求

- Linux系统 (Ubuntu/Debian/CentOS)
- root权限
- Python3环境
- 网络连接 (用于下载Xray-core)

## 使用方法

### 1. 运行脚本
```bash
chmod +x xray_manager.sh
sudo ./xray_manager.sh
```

### 2. 主菜单选项

```
╔══════════════════════════════════════════════════════════════╗
║                    Xray-core 管理脚本                        ║
║                VLESS + Reality + SOCKS5                      ║
╠══════════════════════════════════════════════════════════════╣
║  1. 安装/重装 VLESS + Reality 协议并绑定SOCKS5          ║
║  2. 查看当前运行的 VLESS + Reality 协议配置             ║
║  3. 停止 Xray-core 服务 (同时停止SOCKS5转发)           ║
║  4. 启动 Xray-core 服务 (同时启动SOCKS5转发)           ║
║  5. 重启 Xray-core 服务 (配置保持不变)                 ║
║  6. 查看服务状态                                       ║
║  7. 显示客户端配置                                     ║
║  8. 完全卸载 Xray-core                                 ║
║  0. 退出                                               ║
╚══════════════════════════════════════════════════════════════╝
```

### 3. 首次安装流程

#### 选择 "1. 安装/重装 VLESS + Reality 协议并绑定SOCKS5"

1. **端口检测和选择**
   ```
   === 端口使用情况 ===
   sing-box占用端口: 443 8028
   
   推荐可用端口: 2443 2083 3443 4443 5443
   建议使用端口: 2443
   
   请选择Xray-core监听端口 (直接回车使用推荐端口 2443):
   ```

2. **SOCKS5代理配置**
   ```
   === SOCKS5代理配置 ===
   请输入您的住宅SOCKS5代理信息：
   
   代理服务器地址 (IP或域名): your-proxy.com
   代理端口: 1080
   用户名: your-username
   密码: [隐藏输入]
   ```

3. **Reality伪装域名**
   ```
   请输入伪装域名 (默认: www.bing.com): 
   ```

4. **自动完成安装**
   - 下载并安装Xray-core
   - 生成配置文件
   - 创建systemd服务
   - 启动服务
   - 显示客户端配置信息

## 客户端配置

安装完成后，脚本会显示完整的客户端配置信息：

```
=== Xray-core VLESS + Reality 客户端配置 ===
服务器地址: YOUR_SERVER_IP
端口: 2443
用户ID (UUID): xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
流控: xtls-rprx-vision
传输协议: tcp
传输层安全: reality
SNI: www.bing.com
Fingerprint: chrome
PublicKey: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
ShortId: (留空)
SpiderX: /

注意: 此配置的流量将通过SOCKS5代理(your-proxy.com:1080)转发
```

## 架构优势

### 🎯 **双服务架构**
```
客户端连接选择:
├── sing-box配置 → 直连出口 (主机IP)
└── Xray-core配置 → SOCKS5代理 → 住宅IP出口
```

### ✅ **完全隔离的好处**
1. **独立重启**: 重启Xray-core不影响sing-box服务
2. **独立配置**: 各自管理自己的配置文件
3. **独立端口**: 避免端口冲突
4. **故障隔离**: 一个服务故障不影响另一个
5. **灵活选择**: 客户端可根据需要选择不同出口

## 文件位置

- **脚本文件**: `./xray_manager.sh`
- **Xray-core二进制**: `/usr/local/bin/xray`
- **配置文件**: `/usr/local/etc/xray/config.json`
- **服务文件**: `/etc/systemd/system/xray.service`
- **日志文件**: `/var/log/xray/access.log`, `/var/log/xray/error.log`
- **脚本日志**: `/var/log/xray_manager.log`

## 常用命令

### 服务管理
```bash
# 查看服务状态
systemctl status xray

# 查看日志
journalctl -u xray -f

# 手动重启
systemctl restart xray
```

### 配置管理
```bash
# 验证配置文件
/usr/local/bin/xray -test -config /usr/local/etc/xray/config.json

# 查看配置
cat /usr/local/etc/xray/config.json
```

## 故障排除

### 1. 服务启动失败
```bash
# 查看详细错误
journalctl -u xray --no-pager

# 验证配置文件
/usr/local/bin/xray -test -config /usr/local/etc/xray/config.json
```

### 2. 端口冲突
- 使用脚本重新选择端口
- 检查防火墙设置
- 确认端口未被其他服务占用

### 3. SOCKS5连接失败
- 验证代理服务器信息
- 检查网络连接
- 确认代理服务器状态

## 安全建议

1. **定期更新**: 保持Xray-core版本最新
2. **端口管理**: 使用非标准端口
3. **防火墙**: 配置适当的防火墙规则
4. **监控**: 定期检查服务状态和日志
5. **备份**: 备份重要配置文件

## 与sing-box的对比

| 特性 | Xray-core方案 | sing-box方案 |
|------|---------------|--------------|
| 服务独立性 | ✅ 完全独立 | ❌ 共享服务 |
| 重启影响 | ✅ 无影响 | ❌ 影响所有协议 |
| 配置复杂度 | ✅ 简单 | ✅ 简单 |
| 资源占用 | ⚠️ 略高 | ✅ 较低 |
| 维护成本 | ⚠️ 需要管理两个服务 | ✅ 单一服务 |
| 灵活性 | ✅ 极高 | ⚠️ 中等 |

## 常见问题 (FAQ)

### Q1: 启动/停止Xray-core服务时，SOCKS5服务会同步启停吗？
**A:** 是的，完全同步！
- **启动Xray-core** → SOCKS5代理转发自动激活
- **停止Xray-core** → SOCKS5代理转发自动停止
- **SOCKS5不是独立服务**，而是Xray-core的outbound配置，随主服务一起管理

### Q2: 停止Xray-core服务后重新启动，SOCKS5服务会自动恢复吗？
**A:** 会的，完全自动恢复！
- 配置文件在服务重启后**保持不变**
- SOCKS5代理设置**自动恢复**到之前的配置
- 无需任何手动干预

### Q3: 服务重启后，配置会发生变化吗？客户端需要重新加载配置吗？
**A:** 配置完全不变，客户端无需重新配置！
- ✅ **UUID保持不变** - 客户端连接参数不变
- ✅ **端口保持不变** - 监听端口不变
- ✅ **密钥保持不变** - Reality公钥/私钥不变
- ✅ **SOCKS5设置保持不变** - 代理服务器信息不变
- ✅ **客户端VPN软件无需重新加载配置**

### Q4: 如何确认SOCKS5代理是否正常工作？
**A:** 脚本提供多重验证机制：
1. **启动时自动测试** - 启动服务时自动验证SOCKS5连接
2. **状态检查功能** - 选择菜单项6查看详细状态
3. **连接状态显示** - 实时显示SOCKS5连接是否正常

### Q5: 如果SOCKS5代理服务器出现问题怎么办？
**A:** 脚本会智能处理：
- **启动时检测** - 如果SOCKS5连接异常会显示警告
- **服务仍会启动** - 即使SOCKS5异常，Xray-core仍会尝试运行
- **状态监控** - 通过状态检查功能可以实时监控连接状态
- **重新配置** - 可以选择菜单项1重新配置SOCKS5信息

## 服务同步机制说明

```
Xray-core服务启动 ┌─→ 加载配置文件
                  ├─→ 验证SOCKS5连接
                  ├─→ 启动VLESS监听
                  └─→ 激活SOCKS5转发

Xray-core服务停止 ┌─→ 停止VLESS监听
                  ├─→ 停止SOCKS5转发
                  └─→ 保留配置文件

Xray-core服务重启 ┌─→ 停止所有功能
                  ├─→ 重新加载配置
                  ├─→ 重新验证SOCKS5
                  └─→ 恢复所有功能
```

## 总结

这个Xray-core管理脚本完美解决了您的需求：
- ✅ 与sing-box完全独立，互不影响
- ✅ 智能端口检测，避免冲突
- ✅ 一键安装配置，操作简单
- ✅ VLESS + Reality协议，安全高效
- ✅ 自动绑定SOCKS5，实现住宅IP出口
- ✅ 完整的服务管理功能
- ✅ **SOCKS5与Xray-core完全同步启停**
- ✅ **配置持久化，重启后自动恢复**
- ✅ **客户端无需重新配置**

现在您可以：
1. 保持现有sing-box服务稳定运行
2. 新增Xray-core服务提供住宅IP出口
3. 客户端根据需要选择不同的配置
4. 独立管理和维护两个服务
5. **放心重启服务，配置和SOCKS5会自动恢复**
